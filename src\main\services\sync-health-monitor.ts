import { getDatabase } from '../db/database';
import { getSupabaseClient } from './supabase-client';

export interface SyncHealthStatus {
  isHealthy: boolean;
  lastSync: string | null;
  failedSyncs: number;
  pendingChanges: number;
  errors: string[];
}

export class SyncHealthMonitor {
  static async checkHealth(organizationId: string): Promise<SyncHealthStatus> {
    const errors: string[] = [];
    let isHealthy = true;
    
    try {
      const db = await getDatabase();
      const supabase = getSupabaseClient();
      
      // 1. Check database connection
      const dbTest = db.prepare('SELECT 1').get();
      if (!dbTest) {
        errors.push('Database connection failed');
        isHealthy = false;
      }
      
      // 2. Check Supabase connection
      const { error: supabaseError } = await supabase
        .from('products')
        .select('count')
        .limit(1);
      
      if (supabaseError) {
        errors.push(`Supabase connection failed: ${supabaseError.message}`);
        isHealthy = false;
      }
      
      // 3. Get sync health metrics
      const healthQuery = db.prepare(`
        SELECT 
          MAX(completed_at) as last_sync,
          SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_syncs
        FROM sync_health
        WHERE organization_id = (SELECT id FROM organizations WHERE external_id = ?)
        AND created_at > datetime('now', '-7 days')
      `).get(organizationId);
      
      const failedSyncs = healthQuery?.failed_syncs || 0;
      if (failedSyncs > 3) {
        errors.push(`High sync failure rate: ${failedSyncs} failures in last 7 days`);
        isHealthy = false;
      }
      
      // 4. Check for unsynced local changes
      const localOrg = db.prepare('SELECT id FROM organizations WHERE external_id = ?').get(organizationId);
      const pendingChanges = localOrg ? 
        db.prepare(`
          SELECT COUNT(*) as count FROM products 
          WHERE organization_id = ? 
          AND sync_version > 1
        `).get(localOrg.id).count : 0;
      
      return {
        isHealthy,
        lastSync: healthQuery?.last_sync || null,
        failedSyncs,
        pendingChanges,
        errors
      };
      
    } catch (error) {
      return {
        isHealthy: false,
        lastSync: null,
        failedSyncs: 0,
        pendingChanges: 0,
        errors: [`Health check failed: ${error.message}`]
      };
    }
  }
  
  static async recordSyncAttempt(
    organizationId: string,
    syncType: 'initial' | 'incremental' | 'manual',
    success: boolean,
    itemsSynced: { products: number; colors: number; relationships: number },
    error?: string
  ): Promise<void> {
    try {
      const db = await getDatabase();
      const localOrg = db.prepare('SELECT id FROM organizations WHERE external_id = ?').get(organizationId);
      
      if (localOrg) {
        db.prepare(`
          INSERT INTO sync_health (
            sync_type, sync_status, organization_id,
            started_at, completed_at, error_message,
            items_synced_products, items_synced_colors, items_synced_relationships
          ) VALUES (?, ?, ?, datetime('now'), datetime('now'), ?, ?, ?, ?)
        `).run(
          syncType,
          success ? 'completed' : 'failed',
          localOrg.id,
          error || null,
          itemsSynced.products,
          itemsSynced.colors,
          itemsSynced.relationships
        );
      }
    } catch (err) {
      console.error('[SyncHealth] Failed to record sync attempt:', err);
    }
  }
}
