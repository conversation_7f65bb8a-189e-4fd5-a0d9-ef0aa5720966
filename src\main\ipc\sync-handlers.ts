/**
 * @file sync-handlers.ts
 * @description IPC handlers for sync operations with Supabase integration
 */

import { ipcMain } from 'electron';
import { canRegister<PERSON>andler } from '../utils/ipcRegistry';
import { oauthService } from '../services/oauth.service';
import { realtimeSyncService } from '../services/realtime-sync.service';
import { gdprService } from '../services/gdpr.service';
import { getCurrentOrganizationId, setCurrentOrganizationId } from './organization.ipc';
import { OrganizationService } from '../db/services/organization.service';
import { getDatabase } from '../db/database';
import * as fs from 'fs';

export interface SyncConfig {
  enabled: boolean;
  interval: number;
  lastSync?: string;
}

export function registerSyncHandlers() {
  // Get authentication state - removed incomplete handler, using complete one below

  // Login with Google
  if (canRegisterHandler('sync:login')) {
    ipcMain.handle('sync:login', async () => {
      console.log('[Sync] Login handler called');
      
      // Add overall timeout for the entire login process to prevent hanging
      const loginPromise = (async () => {
        try {
          console.log('[Sync] Step 1: Starting OAuth sign in...');
          const authResult = await oauthService.signInWithGoogle();
          console.log('[Sync] Step 2: Auth service returned:', authResult);
          
          if (!authResult.success) {
            return { success: false, error: authResult.error || 'Authentication failed' };
          }
          
          console.log('[Sync] Step 3: Checking GDPR consent...');
          // Check GDPR consent
          const hasConsent = await oauthService.checkGDPRConsent();
          
          if (!hasConsent) {
            console.log('[Sync] Step 4: GDPR consent required');
            return {
              success: true,
              requiresConsent: true,
              status: authResult.status,
              organizations: authResult.organizations
            };
          }
          
          console.log('[Sync] Step 4: GDPR consent already accepted');
          
          // Handle different authentication statuses
          if (authResult.status === 'authenticated') {
            console.log('[Sync] Step 5: User authenticated, setting up organization...');
            // User is authenticated - determine which organization to use
            let currentOrgId: string | null = null;
            
            if (authResult.organizations?.length === 1) {
              // Single organization - use it
              currentOrgId = authResult.organizations[0].external_id;
            } else if (authResult.organizations && authResult.organizations.length > 1) {
              // Multiple organizations - the OAuth service should have already selected one
              // Check if there's a stored organization that's valid
              const storedOrgId = (global as any).currentOrganizationId;
              const validOrg = authResult.organizations.find(org => org.external_id === storedOrgId);
              if (validOrg) {
                currentOrgId = storedOrgId;
              }
            }
            
            if (currentOrgId) {
              console.log('[Sync] Step 6: Setting current organization:', currentOrgId);
              setCurrentOrganizationId(currentOrgId);
              
              console.log('[Sync] Step 7: Initializing realtime sync service...');
              await realtimeSyncService.initialize(currentOrgId);
              console.log('[Sync] Step 8: Realtime sync service initialized successfully');
            } else {
              console.warn('[Sync] No organization could be determined for authenticated user');
            }
          }
          
          console.log('[Sync] Login process completed successfully');
          return {
            success: true,
            requiresConsent: false,
            status: authResult.status,
            organizations: authResult.organizations || []
          };
        } catch (error: any) {
          console.error('[Sync] Login error:', error);
          return { success: false, error: error.message };
        }
      })();
      
      const timeoutPromise = new Promise<any>((resolve) => 
        setTimeout(() => {
          console.error('[Sync] Login process timeout after 90 seconds');
          resolve({ 
            success: false, 
            error: 'Login process timeout - please try again. If this persists, try restarting ChromaSync.' 
          });
        }, 90000) // 90 second overall timeout
      );
      
      return Promise.race([loginPromise, timeoutPromise]);
    });
  }

  // Accept GDPR consent
  if (canRegisterHandler('sync:accept-gdpr')) {
    ipcMain.handle('sync:accept-gdpr', async (_, ip?: string) => {
      try {
        await oauthService.recordGDPRConsent(ip);
        
        // Start real-time sync after consent
        await realtimeSyncService.initialize();
        
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  // Sign out
  if (canRegisterHandler('sync:logout')) {
    ipcMain.handle('sync:logout', async () => {
      try {
        await realtimeSyncService.cleanup();
        await oauthService.signOut();
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  // Export user data (GDPR)
  if (canRegisterHandler('sync:export-data')) {
    ipcMain.handle('sync:export-data', async () => {
      try {
        const user = await oauthService.getCurrentUser();
        if (!user) {throw new Error('Not authenticated');}
        
        const data = await gdprService.exportUserData(user.id);
        const filePath = await gdprService.getDataExportPath();
        
        await fs.promises.writeFile(filePath, data);
        
        return { success: true, filePath };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  // Request data deletion (GDPR)
  if (canRegisterHandler('sync:delete-account')) {
    ipcMain.handle('sync:delete-account', async () => {
      try {
        const user = await oauthService.getCurrentUser();
        if (!user) {throw new Error('Not authenticated');}
        
        await gdprService.deleteUserData(user.id);
        await realtimeSyncService.cleanup();
        await oauthService.signOut();
        
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  // Get sync configuration
  if (canRegisterHandler('sync:get-config')) {
    ipcMain.handle('sync:get-config', async () => {
      const isActive = realtimeSyncService.isActive();
      return {
        enabled: isActive,
        interval: 300000, // 5 minutes
        lastSync: new Date().toISOString()
      };
    });
  }

  // Check for unsynced local changes
  if (canRegisterHandler('sync:has-unsynced-local-changes')) {
    ipcMain.handle('sync:has-unsynced-local-changes', async () => {
      // In production, this would check for pending changes
      return false;
    });
  }

  // Update sync configuration
  if (canRegisterHandler('sync:update-config')) {
    ipcMain.handle('sync:update-config', async (_, _config: Partial<SyncConfig>) => {
      // Configuration is handled automatically by real-time sync
      return { success: true };
    });
  }

  // Get sync state
  if (canRegisterHandler('sync:get-state')) {
    ipcMain.handle('sync:get-state', async () => {
      const isActive = realtimeSyncService.isActive();
      return {
        isSyncing: isActive,
        lastSync: new Date().toISOString(),
        status: isActive ? 'active' : 'idle'
      };
    });
  }

  // Manual sync
  if (canRegisterHandler('sync:sync')) {
    ipcMain.handle('sync:sync', async () => {
      try {
        // First ensure organizations are synced
        const user = await oauthService.getCurrentUser();
        if (user) {
          console.log('[Sync] Syncing organizations from Supabase for user:', user.id);
          const { getDatabase } = await import('../db/database');
          const database = getDatabase();
          const organizationService = new (await import('../db/services/organization.service')).OrganizationService(database);
          await organizationService.syncOrganizationsFromSupabase(user.id);
        }
        
        const currentOrgId = getCurrentOrganizationId();
        if (!realtimeSyncService.isActive()) {
          if (currentOrgId) {
            await realtimeSyncService.initialize(currentOrgId);
          }
        } else {
          // Service already initialized, just perform sync without re-initialization
          await realtimeSyncService.performInitialSync();
        }
        
        // Check actual Supabase data after sync
        const organizationId = realtimeSyncService.getOrganizationId();
        
        if (user && organizationId) {
          const { getSupabaseClient } = await import('../services/supabase-client');
          const supabase = getSupabaseClient();
          const { count: colorCount } = await supabase
            .from('colors')
            .select('*', { count: 'exact', head: true })
            .eq('organization_id', organizationId);
          
          const { count: productCount } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true })
            .eq('organization_id', organizationId);
            
          console.log(`[Sync] Verification - Colors in Supabase for organization ${organizationId}: ${colorCount}, Products: ${productCount}`);
          
          return {
            success: true,
            message: 'Sync completed successfully',
            counts: {
              colors: colorCount || 0,
              products: productCount || 0,
              datasheets: 0
            }
          };
        }
        
        return {
          success: true,
          message: 'Sync completed successfully'
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message
        };
      }
    });
  }

  // Initialize sync
  if (canRegisterHandler('sync:initialize')) {
    ipcMain.handle('sync:initialize', async () => {
      try {
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          return {
            success: false,
            error: 'Not authenticated'
          };
        }
        
        const hasConsent = await oauthService.checkGDPRConsent();
        if (!hasConsent) {
          return {
            success: false,
            error: 'GDPR consent required'
          };
        }
        
        // First sync organizations to ensure they exist locally
        console.log('[Sync] Syncing organizations before initializing sync service');
        const { getDatabase } = await import('../db/database');
        const db = getDatabase();
        const organizationService = new (await import('../db/services/organization.service')).OrganizationService(db);
        await organizationService.syncOrganizationsFromSupabase(user.id);
        
        const organizationId = getCurrentOrganizationId();
        if (!organizationId) {
          return {
            success: false,
            error: 'No organization selected'
          };
        }
        
        await realtimeSyncService.initialize(organizationId);
        
        return {
          success: true,
          config: {
            enabled: true,
            interval: 300000,
            lastSync: new Date().toISOString()
          }
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message
        };
      }
    });
  }

  // Get auth state
  if (canRegisterHandler('sync:get-auth-state')) {
    ipcMain.handle('sync:get-auth-state', async () => {
      try {
        const user = await oauthService.getCurrentUser();
        const session = await oauthService.getSession();
        const currentOrgId = getCurrentOrganizationId();
        
        // Check organization status
        let status: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated' | undefined;
        let organizations: any[] = [];
        
        if (user) {
          // Get user's organizations
          const db = getDatabase();
          const orgService = new OrganizationService(db);
          
          // Sync organizations from Supabase first
          await orgService.syncOrganizationsFromSupabase(user.id);
          
          // Then get all organizations
          organizations = await orgService.getOrganizationsForUser(user.id);
          
          if (organizations.length === 0) {
            status = 'needs_organization_setup';
          } else if (organizations.length > 1 && !currentOrgId) {
            status = 'needs_organization_selection';
          } else {
            status = 'authenticated';
            
            // If user has one org and no current org is set, set it
            if (!currentOrgId && organizations.length === 1) {
              setCurrentOrganizationId(organizations[0].external_id);
            }
          }
        }
        
        return {
          isAuthenticated: !!user,
          user: user ? {
            id: user.id,
            email: user.email,
            provider: 'google'
          } : null,
          session: session ? {
            access_token: session.access_token,
            expires_at: session.expires_at
          } : null,
          currentOrganizationId: getCurrentOrganizationId(),
          status,
          organizations
        };
      } catch (error) {
        return {
          isAuthenticated: false,
          user: null,
          session: null,
          currentOrganizationId: null
        };
      }
    });
  }

  // OAuth configuration handlers
  if (canRegisterHandler('oauth:configure')) {
    ipcMain.handle('oauth:configure', async (_, settings: {
      authTimeout?: number;
      sessionTimeout?: number;
      sessionWarningTime?: number;
      autoLogoutEnabled?: boolean;
    }) => {
      try {
        oauthService.configureOAuth(settings);
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  if (canRegisterHandler('oauth:get-config')) {
    ipcMain.handle('oauth:get-config', async () => {
      try {
        const config = oauthService.getConfiguration();
        return { success: true, config };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  if (canRegisterHandler('oauth:update-activity')) {
    ipcMain.handle('oauth:update-activity', async () => {
      try {
        oauthService.updateActivity();
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  if (canRegisterHandler('oauth:reset-auth-loop')) {
    ipcMain.handle('oauth:reset-auth-loop', async () => {
      try {
        oauthService.resetAuthLoopDetection();
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  if (canRegisterHandler('oauth:recover-auth')) {
    ipcMain.handle('oauth:recover-auth', async () => {
      try {
        const result = await oauthService.attemptAuthRecovery();
        return result;
      } catch (error: any) {
        return { success: false, message: error.message };
      }
    });
  }

  if (canRegisterHandler('oauth:check-health')) {
    ipcMain.handle('oauth:check-health', async () => {
      try {
        const health = await oauthService.checkAuthHealth();
        return { success: true, health };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  console.log('[Sync] Supabase sync handlers registered');
}
