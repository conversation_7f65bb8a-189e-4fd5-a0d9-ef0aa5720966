/**
 * @file enhanced-realtime-sync.service.ts
 * @description Enhanced version of realtime-sync.service.ts with enterprise-grade migration handling
 * This is a partial refactor focusing on the migration system integration
 */

import { EnterpriseMigrationRunner, MigrationProgress } from '../db/migrations/core/enterprise-migration-runner';
import { getDatabase } from '../db/database';
import * as path from 'path';

/**
 * Enhanced migration management for RealtimeSyncService
 */
export class EnhancedMigrationManager {
  private migrationRunner: EnterpriseMigrationRunner | null = null;
  private migrationProgress: MigrationProgress | null = null;
  private progressCallback?: (progress: MigrationProgress) => void;

  constructor(progressCallback?: (progress: MigrationProgress) => void) {
    this.progressCallback = progressCallback;
  }

  /**
   * Initialize migration runner with database connection
   */
  async initializeMigrationRunner(): Promise<void> {
    try {
      const db = await getDatabase();
      this.migrationRunner = new EnterpriseMigrationRunner(db, (progress) => {
        this.migrationProgress = progress;
        if (this.progressCallback) {
          this.progressCallback(progress);
        }
        console.log(`[EnhancedMigrations] Progress: ${progress.phase} - ${progress.progress}%`);
      });
      
      console.log('[EnhancedMigrations] Migration runner initialized successfully');
    } catch (error) {
      console.error('[EnhancedMigrations] Failed to initialize migration runner:', error);
      throw new Error(`Migration system initialization failed: ${error}`);
    }
  }

  /**
   * Run all pending migrations with enhanced error handling
   */
  async runMigrations(): Promise<{ success: boolean; results?: any; error?: string }> {
    if (!this.migrationRunner) {
      await this.initializeMigrationRunner();
    }

    if (!this.migrationRunner) {
      return {
        success: false,
        error: 'Migration runner not available'
      };
    }

    try {
      // Get migrations directory path
      const migrationsPath = path.join(__dirname, '../db/migrations');
      
      console.log('[EnhancedMigrations] Starting migration process...');
      
      // Run all migrations
      const results = await this.migrationRunner.runAllMigrations(migrationsPath);
      
      if (results.failed > 0) {
        console.error(`[EnhancedMigrations] Migration process completed with ${results.failed} failures`);
        return {
          success: false,
          results,
          error: `${results.failed} migrations failed. Check logs for details.`
        };
      }
      
      console.log(`[EnhancedMigrations] Migration process completed successfully: ${results.successful} migrations applied`);
      
      // Verify database integrity after migrations
      const integrityCheck = this.migrationRunner.verifyDatabaseIntegrity();
      if (!integrityCheck.valid) {
        console.error('[EnhancedMigrations] Database integrity check failed:', integrityCheck.errors);
        return {
          success: false,
          results,
          error: `Database integrity check failed: ${integrityCheck.errors.join(', ')}`
        };
      }
      
      return {
        success: true,
        results
      };
      
    } catch (error) {
      console.error('[EnhancedMigrations] Migration process failed:', error);
      return {
        success: false,
        error: `Migration process failed: ${error}`
      };
    }
  }

  /**
   * Get current migration status
   */
  getMigrationStatus() {
    if (!this.migrationRunner) {
      return {
        initialized: false,
        error: 'Migration runner not initialized'
      };
    }

    return {
      initialized: true,
      status: this.migrationRunner.getMigrationStatus(),
      progress: this.migrationProgress,
      history: this.migrationRunner.getMigrationHistory()
    };
  }

  /**
   * Handle specific migration 9 issue with safe column addition
   */
  async handleMigration9Safely(): Promise<{ success: boolean; error?: string }> {
    try {
      const db = await getDatabase();
      
      // Check if device_id columns already exist
      const tablesWithDeviceId = ['products', 'colors'];
      const results: { table: string; exists: boolean; added: boolean }[] = [];
      
      for (const tableName of tablesWithDeviceId) {
        try {
          // Check if column exists
          const columns = db.pragma(`table_info(${tableName})`);
          const deviceIdExists = columns.some((col: any) => col.name === 'device_id');
          
          if (!deviceIdExists) {
            // Add column safely
            db.exec(`ALTER TABLE ${tableName} ADD COLUMN device_id TEXT`);
            console.log(`[EnhancedMigrations] Added device_id column to ${tableName}`);
            results.push({ table: tableName, exists: false, added: true });
          } else {
            console.log(`[EnhancedMigrations] device_id column already exists in ${tableName}`);
            results.push({ table: tableName, exists: true, added: false });
          }
        } catch (error) {
          console.error(`[EnhancedMigrations] Error handling device_id for ${tableName}:`, error);
          throw new Error(`Failed to handle device_id column for ${tableName}: ${error}`);
        }
      }
      
      // Update migration record to mark as completed
      try {
        db.prepare(`
          INSERT OR REPLACE INTO migrations (id, filename, status, applied_at)
          VALUES (9, '009_production_sync_compatibility.sql', 'completed', ?)
        `).run(new Date().toISOString());
        
        console.log('[EnhancedMigrations] Migration 9 marked as completed');
      } catch (error) {
        console.warn('[EnhancedMigrations] Could not update migration record:', error);
      }
      
      return {
        success: true
      };
      
    } catch (error) {
      console.error('[EnhancedMigrations] Failed to handle Migration 9 safely:', error);
      return {
        success: false,
        error: `Migration 9 handling failed: ${error}`
      };
    }
  }

  /**
   * Cleanup old migration data
   */
  async cleanupMigrationData(olderThanDays: number = 30): Promise<void> {
    if (!this.migrationRunner) {
      console.warn('[EnhancedMigrations] Migration runner not initialized, skipping cleanup');
      return;
    }

    try {
      this.migrationRunner.cleanupCheckpoints(olderThanDays);
      console.log(`[EnhancedMigrations] Cleaned up migration data older than ${olderThanDays} days`);
    } catch (error) {
      console.error('[EnhancedMigrations] Error during migration cleanup:', error);
    }
  }
}

/**
 * Integration helper for existing RealtimeSyncService
 */
export class MigrationIntegrationHelper {
  private enhancedManager: EnhancedMigrationManager;

  constructor() {
    this.enhancedManager = new EnhancedMigrationManager((progress) => {
      // Log progress for monitoring
      console.log(`[MigrationIntegration] ${progress.phase}: ${progress.currentMigration}/${progress.totalMigrations} (${progress.progress}%)`);
      
      if (progress.errors.length > 0) {
        console.error('[MigrationIntegration] Migration errors:', progress.errors);
      }
      
      if (progress.warnings.length > 0) {
        console.warn('[MigrationIntegration] Migration warnings:', progress.warnings);
      }
    });
  }

  /**
   * Replace the existing migration logic in RealtimeSyncService
   */
  async runEnhancedMigrations(): Promise<boolean> {
    try {
      console.log('[MigrationIntegration] Starting enhanced migration process...');
      
      // First, try to handle Migration 9 specifically (the problematic one)
      const migration9Result = await this.enhancedManager.handleMigration9Safely();
      if (!migration9Result.success) {
        console.error('[MigrationIntegration] Failed to handle Migration 9:', migration9Result.error);
        // Don't fail completely, try to continue with other migrations
      }
      
      // Run all migrations with enhanced system
      const result = await this.enhancedManager.runMigrations();
      
      if (!result.success) {
        console.error('[MigrationIntegration] Enhanced migration process failed:', result.error);
        return false;
      }
      
      console.log('[MigrationIntegration] Enhanced migration process completed successfully');
      
      // Cleanup old migration data
      await this.enhancedManager.cleanupMigrationData();
      
      return true;
      
    } catch (error) {
      console.error('[MigrationIntegration] Enhanced migration process error:', error);
      return false;
    }
  }

  /**
   * Get migration status for monitoring
   */
  getMigrationStatus() {
    return this.enhancedManager.getMigrationStatus();
  }
}

/**
 * Factory function to create migration integration helper
 */
export function createMigrationIntegration(): MigrationIntegrationHelper {
  return new MigrationIntegrationHelper();
}

/**
 * Example usage in RealtimeSyncService initialization:
 * 
 * ```typescript
 * // In RealtimeSyncService.initialize()
 * const migrationHelper = createMigrationIntegration();
 * const migrationSuccess = await migrationHelper.runEnhancedMigrations();
 * 
 * if (!migrationSuccess) {
 *   throw new Error('Database migration failed - cannot initialize sync service');
 * }
 * ```
 */
