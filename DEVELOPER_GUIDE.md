# ChromaSync Developer Guide

Complete developer documentation for ChromaSync's architecture, development setup, and best practices.

## Table of Contents

1. [Development Setup](#development-setup)
2. [Architecture Overview](#architecture-overview)
3. [Multi-Tenant Database Design](#multi-tenant-database-design)
4. [Organization System](#organization-system)
5. [Database Guide](#database-guide)
6. [Email Service Setup](#email-service-setup)
7. [Testing Strategy](#testing-strategy)
8. [Performance Optimization](#performance-optimization)
9. [Debugging & Troubleshooting](#debugging--troubleshooting)
10. [Code Standards](#code-standards)
11. [Release Process](#release-process)

---

## Development Setup

### Prerequisites
- **Node.js 18+** and **npm 9+**
- **Git** for version control
- **SQLite** support (included with Node.js)
- Platform-specific build tools:
  - **Windows**: Visual Studio Build Tools
  - **macOS**: Xcode Command Line Tools
  - **Linux**: build-essential package

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/chromasync.git
cd chromasync

# Install dependencies
npm install

# Start development environment
npm run dev
```

### Development Commands

#### Core Development
```bash
npm install          # Install dependencies
npm run dev          # Start dev server with hot reload
npm run build        # Build for production
npm start            # Build + start with debugging enabled
```

#### Testing
```bash
npm test             # Run all tests
npm run test:perf    # Performance benchmarks with ts-node
npm run test:perf:full # Full performance test (up to 100k colors)
npm run test:perf:quick # Quick performance test
```

#### Production Build
```bash
npm run package      # Build installer for all platforms
npm run package:win  # Windows installer only
npm run package:mac  # macOS installer only
npm run package:linux # Linux installer only
```

### Environment Configuration

Create `.env` file in project root:
```bash
# Required for development
NODE_ENV=development

# Optional: Cloud sync development
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Optional: Email testing
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-app-password
```

---

## Architecture Overview

### Electron Process Architecture

ChromaSync follows strict process separation for security and performance:

```
┌─────────────────────────────────────────┐
│ Renderer Process (React UI)            │
│ ├─ React Components                    │
│ ├─ Zustand State Management            │
│ ├─ No direct database access           │
│ └─ IPC communication only              │
├─────────────────────────────────────────┤
│ Preload Script (Security Bridge)       │
│ ├─ contextBridge API exposure          │
│ ├─ Type-safe IPC channels              │
│ └─ No Node.js API access              │
├─────────────────────────────────────────┤
│ Main Process (Node.js Backend)         │
│ ├─ Database operations                 │
│ ├─ File system access                  │
│ ├─ Network communication               │
│ ├─ Cloud sync services                 │
│ └─ Native OS integration               │
└─────────────────────────────────────────┘
```

### Key Directories

```
src/
├── main/                    # Main process (Node.js)
│   ├── db/                  # Database layer
│   │   ├── services/        # Business logic services
│   │   └── migrations/      # Database migrations
│   ├── ipc/                 # IPC handlers
│   ├── services/            # Cloud services
│   │   ├── supabase-client.ts
│   │   ├── oauth.service.ts
│   │   ├── realtime-sync.service.ts
│   │   └── gdpr.service.ts
│   └── utils/               # Utilities
├── renderer/                # UI process (React)
│   ├── components/          # React components
│   ├── store/               # Zustand state stores
│   ├── hooks/               # Custom React hooks
│   └── utils/               # UI utilities
├── preload/                 # Security bridge
│   ├── index.ts             # Main preload script
│   └── sync-api.ts          # Sync API definitions
└── shared/                  # Shared code
    ├── types/               # TypeScript definitions
    ├── constants/           # Shared constants
    └── utils/               # Shared utilities
```

### Critical Architecture Rules

1. **Database access ONLY in main process** - Never attempt database operations in renderer
2. **All IPC through preload** - Use established channel patterns in `src/shared/constants/channels.ts`
3. **No Node.js APIs in renderer** - Use IPC handlers for file system, native features
4. **Type safety required** - No `any` types, full TypeScript coverage
5. **Supabase access ONLY in main process** - Never initialize Supabase client in renderer
6. **Sync through services** - Use RealtimeSyncService for all cloud operations
7. **Offline-first design** - Local database is always the primary source of truth
8. **GDPR compliance** - Always check consent before sync operations
9. **Snake case for SQL** - All database queries must use snake_case naming
10. **Parameterized queries mandatory** - Prevent SQL injection

### State Management

#### Zustand Stores (`src/renderer/store/`)
- **color.store.ts**: Color data and operations
- **product.store.ts**: Product management
- **sync.store.ts**: Authentication and sync state
- **organization.store.ts**: Multi-tenant organization data
- **settings.store.ts**: Application preferences

#### Store Pattern Example
```typescript
export const useColorStore = create<ColorState>()((set, get) => ({
  colors: [],
  loading: false,
  error: null,

  // Actions
  fetchColors: async () => {
    set({ loading: true, error: null });
    try {
      const result = await window.colorAPI.getColors();
      if (result.success) {
        set({ colors: result.data, loading: false });
      } else {
        set({ error: result.error, loading: false });
      }
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Unknown error', 
        loading: false 
      });
    }
  },

  addColor: async (color: Partial<Color>) => {
    const result = await window.colorAPI.addColor(color);
    if (result.success) {
      set(state => ({ 
        colors: [...state.colors, result.data] 
      }));
    }
    return result;
  }
}));
```

---

## Multi-Tenant Database Design

### Design Principles

ChromaSync implements a multi-tenant architecture that enables organizations to collaborate on color and product management while maintaining complete data isolation.

#### Organization-First Architecture
Every piece of data is scoped to an organization, not individual users. This enables:
- Natural team collaboration
- Simplified permission management  
- Clear data ownership boundaries
- Easier compliance with data regulations

#### Hybrid Storage Model
- **Local SQLite**: Primary data store for offline-first functionality
- **Supabase PostgreSQL**: Cloud synchronization and team collaboration
- **Bidirectional Sync**: Seamless data flow between local and cloud

#### Schema Design Pattern
**Decision**: Shared schema with discriminator column (`organization_id`)

**Benefits**:
- Simple backup and maintenance
- Easy cross-organization queries (for admins)
- Efficient resource utilization
- Standard approach for SaaS applications

### Multi-Tenant Schema

#### Organizations Table
```sql
CREATE TABLE organizations (
    id INTEGER PRIMARY KEY,                    -- Local SQLite
    external_id TEXT UNIQUE NOT NULL,         -- UUID for sync
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,                -- URL-friendly identifier
    plan TEXT DEFAULT 'free',                 -- Subscription tier
    settings JSON DEFAULT '{}',               -- Flexible configuration
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### Data Isolation
All core tables include `organization_id` for complete data isolation:
```sql
-- Colors scoped to organization
CREATE TABLE colors (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    hex TEXT NOT NULL,
    organization_id TEXT NOT NULL,  -- Isolation key
    user_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES organizations(external_id)
);
```

#### Row Level Security (Cloud)
```sql
-- Supabase RLS policies for data isolation
CREATE POLICY "organization_access" ON colors
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.organization_id = colors.organization_id
    )
  );
```

---

## Organization System

### Architecture Overview

ChromaSync implements a comprehensive multi-tenant organization system with team collaboration features supporting workspace creation, member invitation, role-based permissions, and seamless organization switching.

### Backend Components

#### OrganizationService (`src/main/db/services/organization.service.ts`)
- Full CRUD operations for organizations
- Member management with role-based permissions
- Email invitation system using SMTP
- Sync with Supabase for cloud collaboration
- SQLite local database with organization isolation

#### IPC Layer (`src/main/ipc/organization.ipc.ts`)
- Secure bridge between renderer and main process
- Current organization persistence using electron-store
- Authentication integration with OAuth service
- Complete channel definitions for all operations

#### Frontend Store (`src/renderer/store/organization.store.ts`)
- Zustand-based state management
- Organization switching with data reload
- Member management actions
- LocalStorage persistence for last selected org

### Key Features

#### 1. Multi-Tenancy
- Users can belong to multiple organizations
- Each organization has isolated data (colors, products)
- Seamless switching between workspaces
- Organization context persists across sessions

#### 2. Role-Based Access Control
- **Owner**: Full control, billing, can delete organization
- **Admin**: Manage members and data
- **Member**: View and edit data only

#### 3. Invitation System
- Email-based invitations with 7-day expiry
- Custom invitation links (`chromasync://invite/{token}`)
- Pending invitation tracking
- Role assignment at invitation time

#### 4. Plan-Based Limits
- **Free**: 5 team members
- **Team**: 20 team members  
- **Enterprise**: Unlimited members

### Implementation Patterns

#### Organization Context Management
```typescript
// Organization switching with data reload
const switchOrganization = async (organizationId: string) => {
  // Update current organization
  await window.organizationAPI.setCurrentOrganization(organizationId);
  
  // Clear existing data
  useColorStore.getState().clearColors();
  useProductStore.getState().clearProducts();
  
  // Reload data for new organization
  await useColorStore.getState().fetchColors();
  await useProductStore.getState().fetchProducts();
};
```

#### Member Invitation Flow
```typescript
// Send invitation
const inviteResult = await window.organizationAPI.inviteMember({
  email: "<EMAIL>",
  role: "member"
});

// Accept invitation
const acceptResult = await window.organizationAPI.acceptInvitation(token);
```

---

## Email Service Setup

### Development Configuration

#### Environment Variables
Create `.env` file with email service credentials:
```bash
# Email Service Configuration (Development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password  # Use app-specific password
```

#### Development Mode (`npm run dev`)
- Uses `.env` file directly via `dotenv`
- Email credentials loaded from environment variables
- Hot reload includes email configuration changes

### Production Configuration

#### Build Process
1. `npm run build` includes `node scripts/build-config.cjs`
2. Creates `/out/app-config.json` with credentials from `.env`
3. Config file is included in packaged app
4. Runtime loads credentials from environment or config file

#### Config Loading Pattern
```typescript
// config-loader.ts pattern
const loadEmailConfig = () => {
  // Try environment variables first
  if (process.env.SMTP_HOST) {
    return {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      user: process.env.SMTP_USER,
      password: process.env.SMTP_PASSWORD
    };
  }
  
  // Fallback to embedded config
  const configPath = path.join(app.getAppPath(), 'app-config.json');
  if (fs.existsSync(configPath)) {
    return JSON.parse(fs.readFileSync(configPath, 'utf8')).email;
  }
  
  throw new Error('No email configuration found');
};
```

### Security Considerations

⚠️ **WARNING**: Production builds embed credentials in the distributed app!

**Recommended alternatives**:
1. **User-provided credentials**: Let users configure their own SMTP settings
2. **Server proxy**: Route emails through your backend API
3. **Environment-only**: Require email config via environment variables

### Testing Email Service

```bash
# Test email functionality in development
npm run test:email

# Test SMTP connection
npm run test-smtp-connection

# Test production build email
npm run build && npm start
```

---

## Database Guide

### Local Database (SQLite)

#### Architecture
- **Database Engine**: SQLite with better-sqlite3
- **Mode**: WAL (Write-Ahead Logging) for concurrent access
- **Connection Pooling**: Max 5 connections
- **Schema Location**: `scripts/create-optimized-schema.sql`

#### Key Tables
```sql
-- Colors with optimized storage
CREATE TABLE colors (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  hex TEXT NOT NULL,
  rgb_optimized TEXT,  -- JSON: calculated on demand
  cmyk_optimized TEXT, -- JSON: CMYK data
  lab_optimized TEXT,  -- JSON: calculated on demand  
  hsl_optimized TEXT,  -- JSON: calculated on demand
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  organization_id TEXT,
  user_id TEXT,
  device_id TEXT,
  sync_version INTEGER DEFAULT 1
);

-- Products
CREATE TABLE products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  tags TEXT, -- JSON array
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  organization_id TEXT,
  user_id TEXT
);

-- Product-Color associations
CREATE TABLE product_colors (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,
  color_id TEXT NOT NULL,
  custom_name TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (color_id) REFERENCES colors(id)
);
```

#### Database Service Pattern
```typescript
export class ColorService {
  constructor(private db: Database.Database) {}

  async getColors(): Promise<Color[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM colors 
      WHERE organization_id = ? 
      ORDER BY created_at DESC
    `);
    
    const rows = stmt.all(getCurrentOrganizationId());
    return rows.map(this.mapRowToColor);
  }

  async addColor(colorData: Partial<Color>): Promise<Color> {
    const id = generateId();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO colors (
        id, name, hex, cmyk_optimized, 
        created_at, updated_at, organization_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id, 
      colorData.name, 
      colorData.hex,
      JSON.stringify(colorData.cmyk),
      now, 
      now, 
      getCurrentOrganizationId()
    );
    
    return this.getColor(id);
  }
}
```

### Cloud Database (Supabase)

#### Architecture
- **Database**: PostgreSQL with real-time subscriptions
- **Storage**: JSONB for optimized color spaces (75% storage reduction)
- **Security**: Row Level Security (RLS) for data access control
- **Schema**: `scripts/supabase-schema.sql`

#### RLS Policies
```sql
-- Enable RLS on colors table
ALTER TABLE colors ENABLE ROW LEVEL SECURITY;

-- Organization-scoped access
CREATE POLICY "organization_access" ON colors
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM user_profiles 
      WHERE user_id = auth.uid()
    )
  );
```

#### Sync Strategy
- **Offline-first**: Local SQLite is the source of truth
- **Bidirectional sync**: Real-time updates in both directions
- **Conflict resolution**: Last-write-wins with device attribution
- **Batch operations**: 500ms debounced updates for API efficiency
- **Color space optimization**: Only CMYK synced (RGB/LAB/HSL calculated locally)

---

## Testing Strategy

### Test Structure
```
src/test/
├── unit/              # Unit tests for individual functions
├── integration/       # IPC and service integration tests
├── e2e/              # End-to-end application tests
└── performance/      # Performance benchmarks
```

### Unit Testing with Vitest
```typescript
// Example: Color conversion tests
import { describe, test, expect } from 'vitest';
import { hexToRgb, rgbToCmyk } from '../src/shared/utils/color/conversion';

describe('Color Conversion', () => {
  test('hexToRgb converts correctly', () => {
    expect(hexToRgb('#FF5733')).toEqual({ r: 255, g: 87, b: 51 });
    expect(hexToRgb('#000000')).toEqual({ r: 0, g: 0, b: 0 });
    expect(hexToRgb('#FFFFFF')).toEqual({ r: 255, g: 255, b: 255 });
  });

  test('rgbToCmyk converts correctly', () => {
    const cmyk = rgbToCmyk(255, 87, 51);
    expect(cmyk.c).toBeCloseTo(0, 1);
    expect(cmyk.m).toBeCloseTo(66, 1);
    expect(cmyk.y).toBeCloseTo(80, 1);
    expect(cmyk.k).toBeCloseTo(0, 1);
  });
});
```

### Integration Testing
```typescript
// Example: IPC integration test
import { test, expect } from 'vitest';
import { app, BrowserWindow } from 'electron';

test('color IPC operations', async () => {
  const testColor = {
    name: 'Test Color',
    hex: '#FF5733'
  };

  // Test adding color
  const addResult = await window.colorAPI.addColor(testColor);
  expect(addResult.success).toBe(true);
  expect(addResult.data.name).toBe('Test Color');

  // Test getting colors
  const getResult = await window.colorAPI.getColors();
  expect(getResult.success).toBe(true);
  expect(getResult.data.length).toBeGreaterThan(0);
});
```

### Performance Testing
```typescript
// Example: Performance benchmark
import { performance } from 'perf_hooks';

test('color processing performance', async () => {
  const start = performance.now();
  
  // Generate 10,000 test colors
  const colors = Array.from({ length: 10000 }, (_, i) => ({
    name: `Color ${i}`,
    hex: `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
  }));

  // Import them
  await window.colorAPI.importColors(colors);
  
  const end = performance.now();
  expect(end - start).toBeLessThan(2000); // Should complete in under 2 seconds
});
```

### E2E Testing with Playwright
```typescript
// Example: E2E test
import { test, expect } from '@playwright/test';

test('color management workflow', async ({ page }) => {
  await page.goto('/');
  
  // Add a color
  await page.click('[data-testid="add-color-button"]');
  await page.fill('[data-testid="color-name-input"]', 'Test Color');
  await page.fill('[data-testid="color-hex-input"]', '#FF5733');
  await page.click('[data-testid="save-color-button"]');
  
  // Verify color appears in list
  await expect(page.locator('[data-testid="color-list"]')).toContainText('Test Color');
});
```

---

## Performance Optimization

### Database Performance
```typescript
// Use prepared statements for repeated queries
class OptimizedColorService {
  private getColorsStmt = this.db.prepare(`
    SELECT * FROM colors 
    WHERE organization_id = ? 
    ORDER BY created_at DESC 
    LIMIT ? OFFSET ?
  `);

  async getColorsPaginated(organizationId: string, limit: number, offset: number) {
    return this.getColorsStmt.all(organizationId, limit, offset);
  }
}

// Use transactions for bulk operations
async importColors(colors: Color[]): Promise<void> {
  const transaction = this.db.transaction((colors: Color[]) => {
    const stmt = this.db.prepare('INSERT INTO colors (name, hex, ...) VALUES (?, ?, ...)');
    for (const color of colors) {
      stmt.run(color.name, color.hex, ...);
    }
  });
  
  transaction(colors);
}
```

### UI Performance
```typescript
// Virtual scrolling for large lists
import { useVirtualizer } from '@tanstack/react-virtual';

const ColorList = ({ colors }: { colors: Color[] }) => {
  const parentRef = useRef<HTMLDivElement>(null);
  
  const virtualizer = useVirtualizer({
    count: colors.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
    overscan: 10
  });

  return (
    <div ref={parentRef} className="h-400 overflow-auto">
      <div style={{ height: virtualizer.getTotalSize() }}>
        {virtualizer.getVirtualItems().map(virtualItem => (
          <ColorRow 
            key={virtualItem.key}
            color={colors[virtualItem.index]}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualItem.size,
              transform: `translateY(${virtualItem.start}px)`
            }}
          />
        ))}
      </div>
    </div>
  );
};
```

### Memory Management
```typescript
// Proper cleanup in React components
const ColorViewer = () => {
  useEffect(() => {
    const unsubscribe = window.colorAPI.onColorUpdate((color) => {
      // Handle real-time updates
    });

    return () => {
      unsubscribe(); // Critical: cleanup subscriptions
    };
  }, []);
};

// Debounced operations
import { debounce } from 'lodash';

const debouncedSearch = debounce(async (query: string) => {
  const results = await window.colorAPI.searchColors(query);
  setSearchResults(results);
}, 300);
```

---

## Debugging & Troubleshooting

### Development Debugging

#### Enable Debug Logging
```bash
# Start with debug logging
npm start -- --enable-logging --verbose

# Enable DevTools
npm start -- --enable-logging --remote-debugging-port=9222
```

#### Electron DevTools
```typescript
// In main process (for development only)
if (process.env.NODE_ENV === 'development') {
  mainWindow.webContents.openDevTools();
}

// In renderer process
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', { colors, products, syncState });
}
```

#### Database Debugging
```typescript
// Enable SQLite logging
const db = new Database(dbPath, { 
  verbose: process.env.NODE_ENV === 'development' ? console.log : undefined 
});

// Query debugging
const debugQuery = (sql: string, params: any[]) => {
  console.log('[DB Query]:', sql);
  console.log('[DB Params]:', params);
  const start = performance.now();
  const result = stmt.all(...params);
  console.log('[DB Time]:', `${performance.now() - start}ms`);
  return result;
};
```

### Common Issues

#### Database Locked
```bash
# Check for other instances
ps aux | grep chromasync

# Clear lock files
rm ~/Library/Application\ Support/chroma-sync/*.lock
```

#### Sync Issues
```typescript
// Debug sync state
const debugSync = async () => {
  const authState = await window.syncAPI.getAuthState();
  const syncState = await window.syncAPI.getState();
  
  console.log('Auth State:', authState);
  console.log('Sync State:', syncState);
  
  if (!authState.authenticated) {
    console.log('User not authenticated');
  } else if (!syncState.isSyncing) {
    console.log('Sync not active');
  }
};
```

#### Memory Leaks
```typescript
// Monitor memory usage
const monitorMemory = () => {
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const memUsage = process.memoryUsage();
      console.log('Memory:', {
        heap: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
      });
    }, 5000);
  }
};
```

---

## Code Standards

### Critical Architecture Rules

**These rules must be followed to maintain ChromaSync's security and performance:**

1. **Database access ONLY in main process** - Never attempt database operations in renderer
2. **All IPC through preload** - Use established channel patterns in `src/shared/constants/channels.ts`
3. **No Node.js APIs in renderer** - Use IPC handlers for file system, native features
4. **Type safety required** - No `any` types, full TypeScript coverage
5. **Supabase access ONLY in main process** - Never initialize Supabase client in renderer
6. **Sync through services** - Use RealtimeSyncService for all cloud operations
7. **Offline-first design** - Local database is always the primary source of truth
8. **GDPR compliance** - Always check consent before sync operations
9. **Snake case for SQL** - All database queries must use snake_case naming
10. **Parameterized queries mandatory** - Prevent SQL injection

### Common Pitfalls to Avoid

1. Don't access `window.api` directly - use the typed stores
2. Don't perform synchronous file operations in renderer
3. Don't store large datasets in React state - use virtual scrolling
4. Don't bypass the IPC security model
5. Don't forget to handle errors with try/catch blocks (required for async/IPC operations)
6. Don't create duplicate implementations - check existing components first
7. Don't use hardcoded styles - prefer design tokens from `src/renderer/styles/tokens/`
8. Don't use camelCase in SQL - use snake_case as enforced by coding standards
9. Don't create monolithic components - follow modular separation principles

### TypeScript Standards
```typescript
// ✅ Good: Explicit types
interface Color {
  id: string;
  name: string;
  hex: string;
  rgb: { r: number; g: number; b: number };
}

// ❌ Bad: Any types
const color: any = getColor();

// ✅ Good: Error handling
async function safeOperation(): Promise<Result<Data, Error>> {
  try {
    const data = await riskyOperation();
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
```

### Naming Conventions
```typescript
// Components: PascalCase
export const ColorPicker: React.FC = () => {};

// Functions/variables: camelCase  
const calculateContrastRatio = (color1: Color, color2: Color) => {};

// Constants: UPPER_SNAKE_CASE
const MAX_COLOR_COUNT = 100000;

// Database fields: snake_case
const query = 'SELECT color_name FROM colors WHERE created_at > ?';
```

### IPC Patterns
```typescript
// ✅ Good: Type-safe IPC
declare global {
  interface Window {
    colorAPI: {
      getColors(): Promise<ApiResponse<Color[]>>;
      addColor(color: Partial<Color>): Promise<ApiResponse<Color>>;
    };
  }
}

// ✅ Good: Error handling in IPC handlers
ipcMain.handle('color:get-all', async () => {
  try {
    const colors = await colorService.getColors();
    return { success: true, data: colors };
  } catch (error) {
    console.error('Get colors error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
});
```

---

## Release Process

### Version Management
ChromaSync follows [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Pre-release Checklist
```bash
# 1. Run full test suite
npm test
npm run test:e2e
npm run test:perf

# 2. Code quality checks
npm run lint
npm run typecheck

# 3. Security audit
npm audit
npm run audit:fix

# 4. Performance check
npm run test:perf:full

# 5. Build verification
npm run build
npm run package
```

### Release Steps
```bash
# 1. Update version
npm version patch|minor|major

# 2. Update changelog
# Edit CHANGELOG.md with new version

# 3. Create release tag
git tag -a v2.0.1 -m "Release v2.0.1"

# 4. Build distribution
npm run package

# 5. Create GitHub release
gh release create v2.0.1 \
  --title "ChromaSync v2.0.1" \
  --notes-file RELEASE_NOTES.md \
  dist/*.{exe,dmg,AppImage}
```

### Continuous Integration
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm test
      - run: npm run lint
      - run: npm run build
  
  build:
    needs: test
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run package
```

---

## Development Tips

### Productivity Tools
```bash
# Watch mode for development
npm run dev              # Hot reload
npm run test:watch      # Test watch mode
npm run lint:watch      # Continuous linting

# Code generation
npm run generate:component ComponentName
npm run generate:store StoreName
npm run generate:service ServiceName
```

### VS Code Configuration
```json
// .vscode/settings.json
{
  "typescript.preferences.strictPropertyInitialization": true,
  "eslint.autoFixOnSave": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### Git Hooks
```bash
# Setup pre-commit hooks
npx husky install
npx husky add .husky/pre-commit "npm run lint && npm test"
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

This developer guide provides comprehensive information for working with ChromaSync. For specific API details, see API_REFERENCE.md. For deployment information, see DEPLOYMENT_GUIDE.md.