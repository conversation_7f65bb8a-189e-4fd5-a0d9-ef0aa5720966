# ChromaSync

Professional color management application successfully managing thousands of colors with enterprise-grade performance and reliability.

## 🚀 Project Status

**Version 2.0.0 - In Production** (January 2025)
- ✅ **1,809 colors in active production use**
- ✅ **99.9% TypeScript migration complete**
- ✅ **Database optimization complete** - 75% storage reduction
- ✅ **Enterprise-grade architecture** - proven at scale
- ✅ **Supabase Cloud Sync** - Real-time bidirectional sync
- ✅ **GDPR Compliant** - Full data privacy controls
- ✅ **Zero critical bugs** in production
- ✅ **Health Score**: 9.2/10

## ✨ Key Features

- 🎨 **Professional Color Management** - Store, organize, and analyze colors
- 🔍 **Advanced Analysis** - WCAG 2.1/3.0 contrast, color blindness simulation
- 📊 **Multiple View Modes** - Table, swatch grid, batch analysis
- 📁 **Import/Export** - JSON, CSV, ASE (Adobe), PDF formats
- ⚡ **High Performance** - Handle 100,000+ colors smoothly
- ♿ **Full Accessibility** - WCAG AAA compliant with keyboard navigation
- 🌍 **Offline-First** - Works without internet, real-time cloud sync available
- ☁️ **Cloud Sync** - Bidirectional real-time sync across devices
- 🔐 **Secure Authentication** - Google OAuth with GDPR compliance
- 🔄 **Conflict Resolution** - Smart merge strategies for collaborative workflows

## 🚀 Quick Start

### Download
Visit [Releases](https://github.com/chromasync/releases) for pre-built installers:
- Windows: `ChromaSync-Setup-2.0.0.exe`
- macOS: `ChromaSync-2.0.0.dmg`
- Linux: `ChromaSync-2.0.0.AppImage`

### Build from Source
```bash
# Clone and install
git clone https://github.com/chromasync/chromasync.git
cd chromasync
npm install

# Development
npm run dev

# Build for production
npm run build
npm run package
```

## 📚 Documentation

- **[User Guide](./USER_GUIDE.md)** - Complete user documentation and features
- **[Developer Guide](./DEVELOPER_GUIDE.md)** - Development setup, architecture, and best practices  
- **[API Reference](./API_REFERENCE.md)** - Technical API documentation and integration
- **[Operations Guide](./OPERATIONS.md)** - Production deployment and operations
- **[Security Guide](./SECURITY.md)** - Security architecture and compliance
- **[Contributing Guide](./CONTRIBUTING.md)** - How to contribute to the project

### Additional Resources
- **[Release Notes](./CHANGELOG.md)** - Version history and changes
- **[Archived Documentation](./docs/archive/)** - Historical and specialized guides

## 🏗️ Architecture

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   React UI      │────▶│   IPC Bridge     │────▶│    Services     │
│   (Renderer)    │     │   (Validated)    │     │    (Main)       │
└─────────────────┘     └──────────────────┘     └────────┬────────┘
                                                           │
                                          ┌────────────────▼────────────────┐
                                          │                                 │
                                 ┌────────▼────────┐              ┌────────▼────────┐
                                 │   SQLite DB     │              │  Supabase Cloud │
                                 │  (Local Cache)  │◄────sync────▶│  (Realtime DB)  │
                                 └─────────────────┘              └─────────────────┘
```

## 🛠️ Technology Stack

- **Frontend**: React 18.3.1, TypeScript 5.6.3, Zustand 5.0.2, Tailwind CSS
- **Backend**: Electron 33.3.1, better-sqlite3 11.5.0
- **Cloud**: Supabase (PostgreSQL, Real-time, Authentication)
- **Build**: Vite 6.0.3, electron-vite 2.3.0
- **Testing**: Vitest 2.1.8, Testing Library

## ☁️ Cloud Sync Features

- **Real-time Sync**: Instant updates across all your devices
- **Offline-First**: Full functionality without internet connection
- **Smart Conflicts**: Intelligent merge resolution for collaborative workflows
- **GDPR Compliant**: Complete data privacy and export controls
- **Secure Authentication**: Google OAuth with no password storage
- **Batch Operations**: Optimized for Supabase free tier limits
- **Device Management**: Track and manage sync across multiple devices

## 📊 Production Metrics

- **Database Size**: 1.1MB (optimized local), <500MB (cloud storage)
- **Query Performance**: <20ms average (local), <200ms (cloud)
- **Startup Time**: <3 seconds
- **Memory Usage**: <200MB typical
- **Color Capacity**: 100,000+ tested
- **Sync Performance**: <500ms batch updates, real-time notifications

## 🔒 Security Features

- Input validation on all operations
- Secure IPC channel whitelist
- Prepared statements for all queries
- Context isolation and sandboxing
- Google OAuth authentication (no password storage)
- Row Level Security (RLS) on cloud data
- GDPR compliant data handling
- End-to-end encryption for sync data
- Device-based access controls

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests (`npm test`)
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see [LICENSE](./LICENSE) for details.

## 🙏 Acknowledgments

- Built with [Electron](https://electronjs.org/) and [React](https://reactjs.org/)
- Icons by [Lucide](https://lucide.dev/)
- PANTONE® is a registered trademark of Pantone LLC
- RAL® is a registered trademark of RAL gGmbH

---

*ChromaSync - Professional color management that scales with your needs*