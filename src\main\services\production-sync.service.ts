import { getSupabaseClient } from './supabase-client';
import { getDatabase } from '../db/database';
import { machineIdSync } from 'node-machine-id';

interface SyncResult {
  success: boolean;
  errors: string[];
  synced: {
    products: number;
    colors: number;
    relationships: number;
  };
}

export class ProductionSyncService {
  private static instance: ProductionSyncService;
  private userId: string | null = null;
  private organizationId: string | null = null;
  private deviceId = machineIdSync();
  private maxRetries = 3;
  private retryDelay = 2000;

  static getInstance(): ProductionSyncService {
    if (!ProductionSyncService.instance) {
      ProductionSyncService.instance = new ProductionSyncService();
    }
    return ProductionSyncService.instance;
  }

  async initialize(userId: string, organizationId: string): Promise<SyncResult> {
    this.userId = userId;
    this.organizationId = organizationId;
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`[ProductionSync] Attempt ${attempt}/${this.maxRetries}`);
        const result = await this.performSync();
        
        if (result.success) {
          return result;
        }
        
        // If sync reported errors but didn't throw, treat as failure
        lastError = new Error(result.errors.join(', '));
        
      } catch (error) {
        lastError = error as Error;
        console.error(`[ProductionSync] Attempt ${attempt} failed:`, error);
      }
      
      if (attempt < this.maxRetries) {
        const delay = this.retryDelay * attempt;
        console.log(`[ProductionSync] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // All attempts failed
    return {
      success: false,
      errors: [`Sync failed after ${this.maxRetries} attempts: ${lastError?.message}`],
      synced: { products: 0, colors: 0, relationships: 0 }
    };
  }

  private async performSync(): Promise<SyncResult> {
    const errors: string[] = [];
    const synced = { products: 0, colors: 0, relationships: 0 };
    
    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();
      
      // Validate organization exists locally
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (!localOrg) {
        throw new Error(`Organization ${this.organizationId} not found`);
      }
      
      const localOrgId = localOrg.id;
      
      // Check what needs syncing
      const localCounts = {
        products: db.prepare('SELECT COUNT(*) as count FROM products WHERE organization_id = ?').get(localOrgId).count,
        colors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE organization_id = ?').get(localOrgId).count
      };
      
      console.log('[ProductionSync] Local counts:', localCounts);
      
      // Sync products if needed
      if (localCounts.products === 0) {
        const productResult = await this.syncProducts(localOrgId);
        synced.products = productResult.count;
        if (productResult.error) errors.push(productResult.error);
      }
      
      // Sync colors if needed
      if (localCounts.colors === 0) {
        const colorResult = await this.syncColors(localOrgId);
        synced.colors = colorResult.count;
        if (colorResult.error) errors.push(colorResult.error);
      }
      
      // Always sync relationships after products/colors
      if (synced.products > 0 || synced.colors > 0) {
        const relationshipResult = await this.syncProductColors(localOrgId);
        synced.relationships = relationshipResult.count;
        if (relationshipResult.error) errors.push(relationshipResult.error);
      }
      
      // Update sync metadata
      await this.updateSyncMetadata();
      
      return {
        success: errors.length === 0,
        errors,
        synced
      };
      
    } catch (error) {
      console.error('[ProductionSync] Sync error:', error);
      throw error;
    }
  }

  private async syncProducts(localOrgId: number): Promise<{ count: number; error?: string }> {
    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();
      
      // Fetch all products for organization
      const { data: products, error } = await supabase
        .from('products')
        .select('*')
        .eq('organization_id', this.organizationId);
      
      if (error) throw error;
      if (!products || products.length === 0) {
        return { count: 0 };
      }
      
      // Insert products in transaction
      const insertStmt = db.prepare(`
        INSERT OR REPLACE INTO products (
          external_id, name, description, metadata,
          organization_id, user_id, created_by,
          created_at, updated_at, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      db.transaction(() => {
        for (const product of products) {
          // Handle schema differences
          const metadata = {
            ...product.metadata || {},
            sku: product.sku // Store Supabase sku in metadata
          };
          
          insertStmt.run(
            product.external_id,
            product.name,
            product.description || product.sku || '',
            JSON.stringify(metadata),
            localOrgId,
            product.user_id || this.userId,
            product.created_by || product.user_id || this.userId,
            product.created_at || new Date().toISOString(),
            product.updated_at || new Date().toISOString(),
            product.is_active !== false ? 1 : 0
          );
        }
      })();
      
      console.log(`[ProductionSync] Synced ${products.length} products`);
      return { count: products.length };
      
    } catch (error) {
      const message = `Failed to sync products: ${error.message}`;
      console.error(`[ProductionSync] ${message}`);
      return { count: 0, error: message };
    }
  }

  private async syncColors(localOrgId: number): Promise<{ count: number; error?: string }> {
    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();
      
      // Fetch all colors for organization from Supabase
      const { data: colors, error } = await supabase
        .from('colors')
        .select('*')
        .eq('organization_id', this.organizationId);
      
      if (error) throw error;
      if (!colors || colors.length === 0) {
        return { count: 0 };
      }
      
      console.log(`[ProductionSync] Fetched ${colors.length} colors from Supabase`);
      
      // Prepare insert statement for colors
      const insertColorStmt = db.prepare(`
        INSERT OR REPLACE INTO colors (
          external_id, organization_id, source_id, code, hex, 
          display_name, properties, device_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      // Insert colors in transaction
      db.transaction(() => {
        for (const color of colors) {
          insertColorStmt.run(
            color.external_id || color.id,
            localOrgId,
            color.source_id || 1,
            color.code,
            color.hex,
            color.display_name,
            JSON.stringify(color.properties || {}),
            color.device_id || this.deviceId,
            color.created_at || new Date().toISOString(),
            color.updated_at || new Date().toISOString()
          );
        }
      })();
      
      console.log(`[ProductionSync] Synced ${colors.length} colors`);
      return { count: colors.length };
      
    } catch (error) {
      const message = `Failed to sync colors: ${error.message}`;
      console.error(`[ProductionSync] ${message}`);
      return { count: 0, error: message };
    }
  }

  private async syncProductColors(localOrgId: number): Promise<{ count: number; error?: string }> {
    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();
      
      // Get mapping of external IDs to local IDs
      const productMap = new Map();
      const colorMap = new Map();
      
      const products = db.prepare(`
        SELECT id, external_id FROM products WHERE organization_id = ?
      `).all(localOrgId);
      
      for (const p of products) {
        productMap.set(p.external_id, p.id);
      }
      
      const colors = db.prepare(`
        SELECT id, external_id FROM colors WHERE organization_id = ?
      `).all(localOrgId);
      
      for (const c of colors) {
        colorMap.set(c.external_id, c.id);
      }
      
      // Fetch relationships from Supabase
      const { data: relationships, error } = await supabase
        .from('product_colors')
        .select(`
          products!inner(external_id),
          colors!inner(external_id),
          display_order
        `)
        .eq('products.organization_id', this.organizationId);
      
      if (error) throw error;
      if (!relationships || relationships.length === 0) {
        return { count: 0 };
      }
      
      // Insert relationships
      const insertStmt = db.prepare(`
        INSERT OR REPLACE INTO product_colors (product_id, color_id, display_order)
        VALUES (?, ?, ?)
      `);
      
      let count = 0;
      db.transaction(() => {
        for (const rel of relationships) {
          const productId = productMap.get(rel.products.external_id);
          const colorId = colorMap.get(rel.colors.external_id);
          
          if (productId && colorId) {
            insertStmt.run(productId, colorId, rel.display_order || 0);
            count++;
          }
        }
      })();
      
      console.log(`[ProductionSync] Synced ${count} product-color relationships`);
      return { count };
      
    } catch (error) {
      const message = `Failed to sync relationships: ${error.message}`;
      console.error(`[ProductionSync] ${message}`);
      return { count: 0, error: message };
    }
  }

  private async updateSyncMetadata(): Promise<void> {
    try {
      const supabase = getSupabaseClient();
      await supabase
        .from('sync_metadata')
        .upsert({
          user_id: this.userId,
          device_id: this.deviceId,
          last_sync: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });
    } catch (error) {
      console.error('[ProductionSync] Failed to update sync metadata:', error);
    }
  }
}
