# ChromaSync API Reference

Technical reference for ChromaSync's IPC APIs, database schema, and integration points.

## Table of Contents

1. [IPC API Reference](#ipc-api-reference)
2. [Database Schema](#database-schema)
3. [Color Types & Validation](#color-types--validation)
4. [Sync API](#sync-api)
5. [Organization API](#organization-api)
6. [File System API](#file-system-api)
7. [Integration Patterns](#integration-patterns)

---

## IPC API Reference

ChromaSync uses Electron's IPC (Inter-Process Communication) for secure communication between the renderer and main processes.

### Color API

#### `colorAPI.getColors()`
Get all colors from the database.

```typescript
interface ColorResponse {
  success: boolean;
  data?: Color[];
  error?: string;
}

const result = await window.colorAPI.getColors();
```

#### `colorAPI.addColor(color: Partial<Color>)`
Add a new color to the database.

```typescript
const newColor = {
  name: "<PERSON> Primary",
  hex: "#FF5733",
  rgb: { r: 255, g: 87, b: 51 },
  cmyk: { c: 0, m: 66, y: 80, k: 0 }
};

const result = await window.colorAPI.addColor(newColor);
```

#### `colorAPI.updateColor(id: string, updates: Partial<Color>)`
Update an existing color.

```typescript
const result = await window.colorAPI.updateColor("color-id", {
  name: "Updated Color Name"
});
```

#### `colorAPI.deleteColor(id: string)`
Delete a color from the database.

```typescript
const result = await window.colorAPI.deleteColor("color-id");
```

#### `colorAPI.importColors(colors: Color[])`
Bulk import colors.

```typescript
const colors = [
  { name: "Color 1", hex: "#FF0000" },
  { name: "Color 2", hex: "#00FF00" }
];

const result = await window.colorAPI.importColors(colors);
```

### Product API

#### `productAPI.getProducts()`
Get all products.

```typescript
const result = await window.productAPI.getProducts();
```

#### `productAPI.createProduct(product: Partial<Product>)`
Create a new product.

```typescript
const newProduct = {
  name: "Website Design",
  description: "Colors for the main website",
  category: "Digital"
};

const result = await window.productAPI.createProduct(newProduct);
```

#### `productAPI.addColorToProduct(productId: string, colorId: string)`
Associate a color with a product.

```typescript
const result = await window.productAPI.addColorToProduct(
  "product-id", 
  "color-id"
);
```

### Sync API

#### `syncAPI.getAuthState()`
Get current authentication state.

```typescript
interface AuthState {
  authenticated: boolean;
  user?: {
    id: string;
    email: string;
    provider: string;
  };
  session?: {
    access_token: string;
    expires_at: number;
  };
  currentOrganizationId?: string;
}

const authState = await window.syncAPI.getAuthState();
```

#### `syncAPI.login()`
Initiate Google OAuth login.

```typescript
const result = await window.syncAPI.login();
// Returns: { success: boolean, requiresConsent?: boolean }
```

#### `syncAPI.logout()`
Sign out and clear session.

```typescript
const result = await window.syncAPI.logout();
```

#### `syncAPI.sync()`
Manually trigger sync with cloud.

```typescript
const result = await window.syncAPI.sync();
```

### Organization API

#### `organizationAPI.getOrganizations()`
Get user's organizations.

```typescript
const result = await window.organizationAPI.getOrganizations();
```

#### `organizationAPI.createOrganization(org: Partial<Organization>)`
Create a new organization.

```typescript
const newOrg = {
  name: "Design Agency",
  description: "Our creative team"
};

const result = await window.organizationAPI.createOrganization(newOrg);
```

#### `organizationAPI.inviteMember(email: string)`
Invite a member to current organization.

```typescript
const result = await window.organizationAPI.inviteMember("<EMAIL>");
```

---

## Database Schema

### Local SQLite Schema

#### Colors Table
```sql
CREATE TABLE colors (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  hex TEXT NOT NULL,
  rgb_optimized TEXT, -- JSON: optimized color spaces
  cmyk_optimized TEXT, -- JSON: CMYK data
  lab_optimized TEXT,  -- JSON: LAB data
  hsl_optimized TEXT,  -- JSON: HSL data
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  organization_id TEXT,
  user_id TEXT,
  device_id TEXT,
  sync_version INTEGER DEFAULT 1
);
```

#### Products Table
```sql
CREATE TABLE products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  tags TEXT, -- JSON array
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  organization_id TEXT,
  user_id TEXT
);
```

#### Product Colors Table
```sql
CREATE TABLE product_colors (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,
  color_id TEXT NOT NULL,
  custom_name TEXT, -- Product-specific color name
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (color_id) REFERENCES colors(id)
);
```

### Cloud PostgreSQL Schema

#### Colors Table (Supabase)
```sql
CREATE TABLE colors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  hex TEXT NOT NULL,
  color_data JSONB, -- Optimized storage for all color spaces
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization_id UUID REFERENCES organizations(id),
  user_id UUID REFERENCES auth.users(id),
  device_id TEXT,
  sync_version INTEGER DEFAULT 1
);
```

#### Organizations Table
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  external_id TEXT UNIQUE NOT NULL, -- For local reference
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## Color Types & Validation

### TypeScript Interfaces

#### Color Interface
```typescript
interface Color {
  id: string;
  name: string;
  hex: string;
  rgb: {
    r: number; // 0-255
    g: number; // 0-255
    b: number; // 0-255
  };
  cmyk: {
    c: number; // 0-100
    m: number; // 0-100
    y: number; // 0-100
    k: number; // 0-100
  };
  lab: {
    l: number; // 0-100
    a: number; // -128 to 127
    b: number; // -128 to 127
  };
  hsl: {
    h: number; // 0-360
    s: number; // 0-100
    l: number; // 0-100
  };
  createdAt: string;
  updatedAt: string;
  organizationId?: string;
  userId?: string;
}
```

#### Product Interface
```typescript
interface Product {
  id: string;
  name: string;
  description?: string;
  category?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  organizationId?: string;
  userId?: string;
  colors?: Color[]; // Associated colors
}
```

### Color Validation

#### HEX Validation
```typescript
const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;

function validateHex(hex: string): boolean {
  return hexPattern.test(hex);
}
```

#### RGB Validation
```typescript
function validateRGB(r: number, g: number, b: number): boolean {
  return [r, g, b].every(val => 
    Number.isInteger(val) && val >= 0 && val <= 255
  );
}
```

#### CMYK Validation
```typescript
function validateCMYK(c: number, m: number, y: number, k: number): boolean {
  return [c, m, y, k].every(val => 
    typeof val === 'number' && val >= 0 && val <= 100
  );
}
```

### Color Conversion Utilities

```typescript
// Available in src/shared/utils/color/conversion.ts
import { 
  hexToRgb, 
  rgbToHex, 
  rgbToCmyk, 
  cmykToRgb,
  rgbToLab,
  labToRgb,
  rgbToHsl,
  hslToRgb 
} from '../shared/utils/color/conversion';

// Example usage
const rgb = hexToRgb("#FF5733");
const cmyk = rgbToCmyk(rgb.r, rgb.g, rgb.b);
```

---

## Sync API

### Authentication Flow

```typescript
// 1. Check authentication state
const authState = await window.syncAPI.getAuthState();

if (!authState.authenticated) {
  // 2. Initiate login
  const loginResult = await window.syncAPI.login();
  
  if (loginResult.requiresConsent) {
    // 3. Handle GDPR consent
    const consentResult = await window.syncAPI.acceptGDPR();
  }
}

// 4. Initialize sync
const syncResult = await window.syncAPI.initialize();
```

### Real-time Sync Events

```typescript
// Listen for sync status updates
window.syncAPI.onStatusUpdate((status) => {
  console.log('Sync status:', status);
  // status: 'syncing' | 'success' | 'error' | 'idle'
});

// Listen for conflicts
window.syncAPI.onConflicts((conflicts) => {
  console.log('Sync conflicts detected:', conflicts);
});
```

### GDPR Compliance API

```typescript
// Export user data
const exportResult = await window.syncAPI.exportData();
// Returns: { success: boolean, filePath?: string }

// Request account deletion
const deleteResult = await window.syncAPI.deleteAccount();
// Schedules deletion in 30 days

// Check consent status
const authState = await window.syncAPI.getAuthState();
const hasConsent = authState.gdprConsent;
```

---

## Organization API

### Multi-tenant Architecture

```typescript
// Get current organization
const currentOrg = await window.organizationAPI.getCurrentOrganization();

// Switch organization
const switchResult = await window.organizationAPI.switchOrganization("org-id");

// All subsequent API calls are scoped to current organization
const colors = await window.colorAPI.getColors(); // Only org colors
```

### Team Management

```typescript
// Get organization members
const members = await window.organizationAPI.getMembers();

// Invite member
const inviteResult = await window.organizationAPI.inviteMember({
  email: "<EMAIL>",
  role: "member" // or "admin"
});

// Accept invitation (called when user clicks email link)
const acceptResult = await window.organizationAPI.acceptInvitation("token");
```

---

## File System API

### Import/Export Operations

```typescript
// Show file picker for import
const importResult = await window.fileAPI.showOpenDialog({
  filters: [
    { name: 'JSON Files', extensions: ['json'] },
    { name: 'CSV Files', extensions: ['csv'] },
    { name: 'ASE Files', extensions: ['ase'] }
  ]
});

// Export colors to file
const exportResult = await window.fileAPI.exportColors(colors, {
  format: 'json' | 'csv' | 'ase' | 'pdf',
  filePath: '/path/to/export.json'
});
```

### Shared Folder API

```typescript
// Get shared folder path
const sharedPath = await window.sharedFolderAPI.getPath();

// List datasheets
const datasheets = await window.sharedFolderAPI.listDatasheets();

// Open datasheet
const openResult = await window.sharedFolderAPI.openDatasheet("filename.pdf");
```

---

## Integration Patterns

### Plugin Architecture

ChromaSync supports custom integrations through its IPC API:

```typescript
// Example: Custom color picker integration
class CustomColorPicker {
  async addColorFromPicker(): Promise<void> {
    const pickedColor = await this.showColorPicker();
    
    if (pickedColor) {
      const result = await window.colorAPI.addColor({
        name: `Picked Color ${Date.now()}`,
        hex: pickedColor.hex,
        rgb: pickedColor.rgb
      });
      
      if (result.success) {
        this.showSuccessMessage("Color added successfully");
      }
    }
  }
}
```

### Batch Processing

```typescript
// Example: Bulk color processing
class ColorBatchProcessor {
  async processColorBatch(colors: Partial<Color>[]): Promise<void> {
    const batchSize = 100;
    
    for (let i = 0; i < colors.length; i += batchSize) {
      const batch = colors.slice(i, i + batchSize);
      
      try {
        await window.colorAPI.importColors(batch);
        console.log(`Processed batch ${i / batchSize + 1}`);
      } catch (error) {
        console.error(`Batch ${i / batchSize + 1} failed:`, error);
      }
    }
  }
}
```

### Error Handling Patterns

```typescript
// Robust error handling for API calls
async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  fallback?: T
): Promise<T | undefined> {
  try {
    return await apiCall();
  } catch (error) {
    console.error('API call failed:', error);
    
    if (fallback !== undefined) {
      return fallback;
    }
    
    // Show user-friendly error
    window.toastAPI?.showError('Operation failed. Please try again.');
    return undefined;
  }
}

// Usage
const colors = await safeApiCall(
  () => window.colorAPI.getColors(),
  { success: false, data: [], error: 'Failed to load colors' }
);
```

---

## Performance Considerations

### Large Dataset Handling

```typescript
// Virtual scrolling for large color lists
const VIEWPORT_SIZE = 50;

async function getColorPage(offset: number, limit: number): Promise<Color[]> {
  return await window.colorAPI.getColors({
    offset,
    limit,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
}
```

### Debounced Operations

```typescript
// Debounced search
import { debounce } from 'lodash';

const debouncedSearch = debounce(async (query: string) => {
  const results = await window.colorAPI.searchColors(query);
  updateSearchResults(results);
}, 300);
```

### Memory Management

```typescript
// Cleanup subscriptions
class ComponentWithSubscriptions {
  private cleanupFunctions: (() => void)[] = [];
  
  componentDidMount() {
    const unsubscribe = window.syncAPI.onStatusUpdate(this.handleSyncStatus);
    this.cleanupFunctions.push(unsubscribe);
  }
  
  componentWillUnmount() {
    this.cleanupFunctions.forEach(cleanup => cleanup());
  }
}
```