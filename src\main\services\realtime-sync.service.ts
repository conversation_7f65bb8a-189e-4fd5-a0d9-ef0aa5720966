/**
 * @file realtime-sync.service.ts
 * @description Real-time synchronization service with Supabase
 * 
 * Color Space Sync Strategy:
 * - Only CMYK values are synced to Supabase (stored in color_spaces JSONB)
 * - RGB, LAB, and HSL are calculated from hex values in the app
 * - This reduces storage requirements and ensures consistency
 * - CMYK is synced because it requires color profiles for accurate conversion
 */

import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { getSupabaseClient } from './supabase-client';
import { getDatabase } from '../db/database';
import { debounce } from '../utils/debounce';
import { machineIdSync } from 'node-machine-id';
import { net } from 'electron';
import Store from 'electron-store';
import crypto from 'crypto';

export class RealtimeSyncService {
  private channel: RealtimeChannel | null = null;
  private syncQueue = new Map<string, any>();
  private persistentStore = new Store({ name: 'sync-queue' });
  private userId: string | null = null;
  private organizationId: string | null = null; // Added for multi-tenant support
  private deviceId = machineIdSync();
  private isInitialized = false;
  private isSyncing = false;
  
  // Timer and interval tracking for proper cleanup
  private heartbeatInterval: ReturnType<typeof setInterval> | null = null;
  private healthCheckInterval: ReturnType<typeof setInterval> | null = null;
  private reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isReconnecting = false;
  
  // Circuit breaker pattern for repeated failures
  private circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount = 0;
  private maxFailures = 3;
  private circuitBreakerTimeout = 60000; // 1 minute
  private lastFailureTime = 0;
  
  // Network connectivity monitoring
  private isOnline = true;
  private networkCheckInterval: ReturnType<typeof setInterval> | null = null;
  private pendingReconnectOnOnline = false;
  
  // Connection health monitoring
  private connectionHealthInterval: ReturnType<typeof setInterval> | null = null;
  private lastHealthCheckTime = 0;
  private consecutiveHealthCheckFailures = 0;
  private maxHealthCheckFailures = 3;
  
  // Connection timeout handling
  private connectionTimeout: ReturnType<typeof setTimeout> | null = null;
  private readonly CONNECTION_TIMEOUT = 30000; // 30 seconds as recommended by Supabase
  
  // Configurable batch sizes for performance optimization
  private readonly batchSizes = {
    products: 100,      // Products per batch - increased from 10
    colors: 100,        // Colors per batch - increased from 20
    relationships: 100, // Product-color relationships per batch - increased from 50
    supabaseQuery: 1000 // Max items per Supabase query - increased from 100
  };
  
  // Enhanced error tracking and recovery
  private lastError: string | null = null;
  private errorCount = 0;
  private errorHistory: Array<{ timestamp: number; type: string; message: string; context?: string }> = [];
  private readonly MAX_ERROR_HISTORY = 50;
  
  // Error recovery queue for failed operations
  private errorRecoveryQueue = new Map<string, {
    operation: string;
    data: any;
    attempts: number;
    lastAttempt: number;
    maxAttempts: number;
  }>();
  private recoveryInterval: ReturnType<typeof setInterval> | null = null;
  private readonly RECOVERY_INTERVAL_MS = 30000; // 30 seconds
  private readonly MAX_RECOVERY_ATTEMPTS = 5;
  
  // Performance monitoring
  private syncMetrics = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    averageDuration: 0,
    slowOperations: 0, // Operations > 5 seconds
    lastSyncDuration: 0
  };
  
  // Phoenix protocol reference ID management
  private refCounter = 0;
  private pendingRefs = new Map<string, { timestamp: number; type: string }>();
  
  // WebSocket connection logging configuration
  private logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';
  
  /**
   * Enhanced error tracking with context and recovery
   */
  private trackError(error: any, operation: string, context?: string): void {
    this.errorCount++;
    
    // Add to error history
    this.errorHistory.push({
      timestamp: Date.now(),
      type: operation,
      message: error.message || error.toString(),
      context
    });
    
    // Maintain error history size
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift();
    }
    
    // Log for debugging
    console.error(`[Sync] Error in ${operation}${context ? ` (${context})` : ''}:`, error);
  }
  
  /**
   * Enhanced error creation with recovery queueing
   */
  private createUserFriendlyError(error: any, operation: string, data?: any): Error {
    this.trackError(error, operation);
    
    let userMessage = '';
    let canRecover = false;
    
    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      userMessage = 'Network connection issue - please check your internet connection and try again';
      canRecover = true;
    } else if (error.message?.includes('auth') || error.message?.includes('permission')) {
      userMessage = 'Authentication error - please sign in again to continue syncing';
      canRecover = false; // Auth errors need manual intervention
    } else if (error.message?.includes('limit') || error.message?.includes('quota')) {
      userMessage = 'Sync limit reached - upgrade your plan to continue syncing';
      canRecover = false; // Rate limit errors need time or upgrade
    } else if (error.message?.includes('organization')) {
      userMessage = 'Organization not found - please check your organization access';
      canRecover = false; // Org errors need manual intervention
    } else if (error.message?.includes('timeout')) {
      userMessage = 'Sync is taking longer than expected - please try again in a moment';
      canRecover = true;
    } else if (error.message?.includes('connection')) {
      userMessage = 'Connection lost - automatically retrying in the background';
      canRecover = true;
    } else {
      userMessage = `Unable to sync ${operation} - please try again later`;
      canRecover = true;
    }
    
    this.lastError = userMessage;
    
    // Queue for recovery if the error is recoverable and we have data
    if (canRecover && data && this.isRetryableOperation(operation)) {
      this.queueForRecovery(operation, data);
    }
    
    const friendlyError = new Error(userMessage);
    friendlyError.cause = error; // Preserve original error for debugging
    return friendlyError;
  }
  
  /**
   * Check if an operation can be retried automatically
   */
  private isRetryableOperation(operation: string): boolean {
    const retryableOps = [
      'data sync',
      'push local changes',
      'initial sync',
      'realtime sync',
      'batch processing'
    ];
    return retryableOps.some(op => operation.includes(op));
  }
  
  /**
   * Queue a failed operation for automatic recovery
   */
  private queueForRecovery(operation: string, data: any): void {
    const key = `${operation}-${Date.now()}`;
    
    this.errorRecoveryQueue.set(key, {
      operation,
      data,
      attempts: 0,
      lastAttempt: Date.now(),
      maxAttempts: this.MAX_RECOVERY_ATTEMPTS
    });
    
    console.log(`[Sync] Queued ${operation} for automatic recovery (queue size: ${this.errorRecoveryQueue.size})`);
    
    // Start recovery processing if not already running
    if (!this.recoveryInterval) {
      this.startErrorRecovery();
    }
  }
  
  /**
   * Get the last user-friendly error message
   */
  getLastError(): string | null {
    return this.lastError;
  }
  
  /**
   * Clear the last error (call this when operations succeed)
   */
  clearLastError(): void {
    this.lastError = null;
  }
  
  /**
   * Start the error recovery processing
   */
  private startErrorRecovery(): void {
    if (this.recoveryInterval) {
      return; // Already running
    }
    
    console.log('[Sync] Starting error recovery processor');
    
    this.recoveryInterval = setInterval(() => {
      this.processErrorRecovery().catch(error => {
        console.error('[Sync] Error in recovery processor:', error);
      });
    }, this.RECOVERY_INTERVAL_MS);
  }
  
  /**
   * Stop error recovery processing
   */
  private stopErrorRecovery(): void {
    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
      console.log('[Sync] Stopped error recovery processor');
    }
  }
  
  /**
   * Process queued recovery operations
   */
  private async processErrorRecovery(): Promise<void> {
    if (this.errorRecoveryQueue.size === 0) {
      this.stopErrorRecovery(); // Stop when queue is empty
      return;
    }
    
    const now = Date.now();
    const itemsToRetry: Array<[string, any]> = [];
    const itemsToRemove: string[] = [];
    
    // Find items ready for retry
    for (const [key, item] of this.errorRecoveryQueue.entries()) {
      if (item.attempts >= item.maxAttempts) {
        // Max attempts reached - remove from queue
        itemsToRemove.push(key);
        console.log(`[Sync] Giving up on ${item.operation} after ${item.attempts} attempts`);
      } else if (now - item.lastAttempt >= this.calculateRecoveryDelay(item.attempts)) {
        // Ready for retry
        itemsToRetry.push([key, item]);
      }
    }
    
    // Remove exhausted items
    itemsToRemove.forEach(key => this.errorRecoveryQueue.delete(key));
    
    // Process retries
    for (const [key, item] of itemsToRetry) {
      try {
        console.log(`[Sync] Attempting recovery for ${item.operation} (attempt ${item.attempts + 1}/${item.maxAttempts})`);
        
        await this.retryOperation(item.operation, item.data);
        
        // Success - remove from queue
        this.errorRecoveryQueue.delete(key);
        console.log(`[Sync] Successfully recovered ${item.operation}`);
        
      } catch (error) {
        // Update attempt count and timestamp
        item.attempts++;
        item.lastAttempt = now;
        
        console.warn(`[Sync] Recovery attempt ${item.attempts} failed for ${item.operation}:`, error);
        
        // If max attempts reached, it will be removed in the next cycle
      }
    }
    
    console.log(`[Sync] Recovery cycle complete. Queue size: ${this.errorRecoveryQueue.size}`);
  }
  
  /**
   * Calculate exponential backoff delay for recovery attempts
   */
  private calculateRecoveryDelay(attempt: number): number {
    // Exponential backoff: 30s, 60s, 120s, 240s, 480s
    const baseDelay = this.RECOVERY_INTERVAL_MS;
    const exponentialDelay = baseDelay * Math.pow(2, attempt);
    const maxDelay = 10 * 60 * 1000; // Cap at 10 minutes
    
    return Math.min(exponentialDelay, maxDelay);
  }
  
  /**
   * Retry a failed operation based on its type
   */
  private async retryOperation(operation: string, data: any): Promise<void> {
    switch (operation) {
      case 'initial sync':
        await this.performInitialSync();
        break;
        
      case 'push local changes':
        await this.pushLocalChanges();
        break;
        
      case 'batch processing':
        await this.retryBatchProcessing(data);
        break;
        
      case 'realtime sync':
        await this.subscribeToChanges();
        break;
        
      default:
        console.warn(`[Sync] Unknown operation type for recovery: ${operation}`);
        throw new Error(`Unknown operation: ${operation}`);
    }
  }
  
  /**
   * Retry batch processing with the original data
   */
  private async retryBatchProcessing(originalData: any): Promise<void> {
    const { items } = originalData;
    
    if (!items || !Array.isArray(items)) {
      throw new Error('Invalid batch data for retry');
    }
    
    const supabase = getSupabaseClient();
    
    // Process items by type
    const colors = items.filter(item => item.table === 'colors');
    const products = items.filter(item => item.table === 'products');
    
    if (colors.length > 0) {
      const { error } = await supabase.rpc('batch_upsert_colors', {
        p_user_id: this.userId,
        p_organization_id: this.organizationId,
        p_colors: colors.map(c => c.data)
      });
      
      if (error) {
        throw new Error(`Batch color retry failed: ${error.message}`);
      }
    }
    
    if (products.length > 0) {
      const { error } = await supabase
        .from('products')
        .upsert(products.map(p => ({
          ...p.data,
          user_id: this.userId,
          organization_id: this.organizationId
        })), {
          onConflict: 'external_id'
        });
        
      if (error) {
        throw new Error(`Batch product retry failed: ${error.message}`);
      }
    }
  }
  
  /**
   * Get error history for debugging
   */
  getErrorHistory(): Array<{ timestamp: number; type: string; message: string; context?: string }> {
    return [...this.errorHistory];
  }
  
  /**
   * Get current recovery queue status
   */
  getRecoveryStatus(): {
    queueSize: number;
    isProcessing: boolean;
    operations: Array<{ operation: string; attempts: number; maxAttempts: number }>;
  } {
    return {
      queueSize: this.errorRecoveryQueue.size,
      isProcessing: this.recoveryInterval !== null,
      operations: Array.from(this.errorRecoveryQueue.values()).map(item => ({
        operation: item.operation,
        attempts: item.attempts,
        maxAttempts: item.maxAttempts
      }))
    };
  }
  
  /**
   * Manually trigger recovery for all queued operations (for debugging/admin)
   */
  async forceRecovery(): Promise<void> {
    console.log('[Sync] Forcing immediate recovery for all queued operations');
    await this.processErrorRecovery();
  }
  
  /**
   * Clear the recovery queue (for manual intervention)
   */
  clearRecoveryQueue(): void {
    const size = this.errorRecoveryQueue.size;
    this.errorRecoveryQueue.clear();
    this.stopErrorRecovery();
    console.log(`[Sync] Cleared ${size} items from recovery queue`);
  }
  
  /**
   * Track sync operation performance
   */
  private trackSyncPerformance(operation: string, duration: number, success: boolean): void {
    this.syncMetrics.totalSyncs++;
    this.syncMetrics.lastSyncDuration = duration;
    
    if (success) {
      this.syncMetrics.successfulSyncs++;
      this.clearLastError(); // Clear error on success
    } else {
      this.syncMetrics.failedSyncs++;
    }
    
    // Update average duration
    this.syncMetrics.averageDuration = 
      ((this.syncMetrics.averageDuration * (this.syncMetrics.totalSyncs - 1)) + duration) / this.syncMetrics.totalSyncs;
    
    // Track slow operations (> 5 seconds)
    if (duration > 5000) {
      this.syncMetrics.slowOperations++;
      console.warn(`[Sync] Slow operation detected: ${operation} took ${duration}ms`);
    }
    
    // Log performance metrics periodically
    if (this.syncMetrics.totalSyncs % 10 === 0) {
      console.log('[Sync] Performance metrics:', {
        totalSyncs: this.syncMetrics.totalSyncs,
        successRate: `${Math.round((this.syncMetrics.successfulSyncs / this.syncMetrics.totalSyncs) * 100)}%`,
        averageDuration: `${Math.round(this.syncMetrics.averageDuration)}ms`,
        slowOperations: this.syncMetrics.slowOperations
      });
    }
  }
  
  /**
   * Get current sync performance metrics
   */
  getSyncMetrics() {
    return {
      ...this.syncMetrics,
      successRate: this.syncMetrics.totalSyncs > 0 
        ? Math.round((this.syncMetrics.successfulSyncs / this.syncMetrics.totalSyncs) * 100)
        : 0
    };
  }
  
  /**
   * Refresh access token for authenticated Realtime connection
   */
  async refreshAccessToken(): Promise<boolean> {
    try {
      const supabase = getSupabaseClient();
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session?.access_token) {
        console.error('[Sync] Failed to get fresh access token:', error);
        return false;
      }
      
      // Send access token update using Phoenix protocol
      const ref = this.sendPhoenixMessage(`org-${this.organizationId}`, 'access_token', {
        access_token: session.access_token
      });
      
      if (ref) {
        console.log(`[Sync] Access token refresh sent (ref: ${ref})`);
        return true;
      } else {
        console.warn('[Sync] Failed to send access token refresh message');
        return false;
      }
    } catch (error) {
      console.error('[Sync] Error refreshing access token:', error);
      return false;
    }
  }
  
  /**
   * Generate unique reference ID for Phoenix protocol compliance
   */
  private generateRef(): string {
    this.refCounter++;
    return `${Date.now()}-${this.refCounter}`;
  }
  
  /**
   * Track pending message references for acknowledgment handling
   */
  private trackPendingRef(ref: string, type: string): void {
    this.pendingRefs.set(ref, {
      timestamp: Date.now(),
      type
    });
    
    // Clean up old pending refs (older than 30 seconds)
    const now = Date.now();
    for (const [pendingRef, data] of this.pendingRefs.entries()) {
      if (now - data.timestamp > 30000) {
        this.pendingRefs.delete(pendingRef);
      }
    }
  }
  
  /**
   * Configure WebSocket logging level
   */
  setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    this.logLevel = level;
    this.log('info', `[Sync] Log level set to: ${level}`);
  }
  
  /**
   * Enhanced logging with configurable levels
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    if (levels[level] >= levels[this.logLevel]) {
      switch (level) {
        case 'debug': console.debug(message, ...args); break;
        case 'info': console.log(message, ...args); break;
        case 'warn': console.warn(message, ...args); break;
        case 'error': console.error(message, ...args); break;
      }
    }
  }
  
  /**
   * Send Phoenix protocol compliant message
   */
  private sendPhoenixMessage(topic: string, event: string, payload: any = {}): string | null {
    if (!this.channel?.socket) {
      this.log('warn', '[Sync] Cannot send Phoenix message - socket not available');
      return null;
    }
    
    // Check WebSocket readyState before sending
    const socket = (this.channel as any).socket;
    if (!socket || socket.readyState !== 1) { // WebSocket.OPEN = 1
      this.log('warn', `[Sync] Cannot send Phoenix message - WebSocket state: ${socket?.readyState || 'unknown'}`);
      return null;
    }
    
    const ref = this.generateRef();
    const message = {
      topic,
      event,
      payload,
      ref
    };
    
    this.log('debug', `[Sync] Sending Phoenix message:`, message);
    
    try {
      // Use the internal Phoenix socket push method
      if (socket.push) {
        socket.push(message);
        this.trackPendingRef(ref, event);
        this.log('debug', `[Sync] Phoenix message sent successfully (ref: ${ref})`);
        return ref;
      } else {
        this.log('warn', '[Sync] Phoenix socket.push method not available');
        return null;
      }
    } catch (error) {
      this.log('error', '[Sync] Error sending Phoenix message:', error);
      return null;
    }
  }
  
  /**
   * Persistent sync queue management
   */
  private getQueueKey(): string {
    return `queue-${this.organizationId || 'default'}-${this.userId || 'anonymous'}`;
  }
  
  /**
   * Load persistent queue items from storage
   */
  private loadPersistentQueue(): void {
    try {
      const queueKey = this.getQueueKey();
      const persistedItems = this.persistentStore.get(queueKey, {}) as Record<string, any>;
      
      // Load items into memory queue
      for (const [key, item] of Object.entries(persistedItems)) {
        // Add timestamp if not present for cleanup purposes
        if (!item.timestamp) {
          item.timestamp = Date.now();
        }
        
        // Only load items that are less than 24 hours old
        const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours
        if (Date.now() - item.timestamp < MAX_AGE) {
          this.syncQueue.set(key, item);
        }
      }
      
      console.log(`[Sync] Loaded ${this.syncQueue.size} items from persistent queue`);
      
      // Clean up old items from persistent storage
      this.cleanupPersistentQueue();
    } catch (error) {
      console.error('[Sync] Error loading persistent queue:', error);
      // Don't fail initialization if persistent queue loading fails
    }
  }
  
  /**
   * Save current queue state to persistent storage
   */
  private savePersistentQueue(): void {
    try {
      const queueKey = this.getQueueKey();
      const queueData: Record<string, any> = {};
      
      for (const [key, item] of this.syncQueue.entries()) {
        queueData[key] = {
          ...item,
          timestamp: item.timestamp || Date.now()
        };
      }
      
      this.persistentStore.set(queueKey, queueData);
      console.log(`[Sync] Saved ${Object.keys(queueData).length} items to persistent queue`);
    } catch (error) {
      console.error('[Sync] Error saving persistent queue:', error);
      // Don't fail the operation if persistence fails
    }
  }
  
  /**
   * Add item to both memory and persistent queue
   */
  private addToQueue(key: string, item: any): void {
    // Add timestamp for cleanup purposes
    const itemWithTimestamp = {
      ...item,
      timestamp: Date.now()
    };
    
    // Add to memory queue
    this.syncQueue.set(key, itemWithTimestamp);
    
    // Save to persistent storage
    this.savePersistentQueue();
  }
  
  /**
   * Remove item from both memory and persistent queue
   */
  private removeFromQueue(key: string): void {
    this.syncQueue.delete(key);
    this.savePersistentQueue();
  }
  
  /**
   * Clear all queue data (memory and persistent)
   */
  private clearQueue(): void {
    this.syncQueue.clear();
    const queueKey = this.getQueueKey();
    this.persistentStore.delete(queueKey);
    console.log('[Sync] Cleared sync queue and persistent storage');
  }
  
  /**
   * Clean up old items from persistent storage
   */
  private cleanupPersistentQueue(): void {
    try {
      const queueKey = this.getQueueKey();
      const persistedItems = this.persistentStore.get(queueKey, {}) as Record<string, any>;
      const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours
      const now = Date.now();
      
      let cleanedCount = 0;
      const cleanedItems: Record<string, any> = {};
      
      for (const [key, item] of Object.entries(persistedItems)) {
        if (item.timestamp && now - item.timestamp < MAX_AGE) {
          cleanedItems[key] = item;
        } else {
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        this.persistentStore.set(queueKey, cleanedItems);
        console.log(`[Sync] Cleaned up ${cleanedCount} old items from persistent queue`);
      }
    } catch (error) {
      console.error('[Sync] Error during persistent queue cleanup:', error);
    }
  }
  
  /**
   * Get queue statistics for monitoring
   */
  private getQueueStats(): { memorySize: number; persistentSize: number; oldestItem?: number } {
    const queueKey = this.getQueueKey();
    const persistedItems = this.persistentStore.get(queueKey, {}) as Record<string, any>;
    
    let oldestTimestamp: number | undefined;
    for (const item of Object.values(persistedItems)) {
      if (item.timestamp) {
        if (!oldestTimestamp || item.timestamp < oldestTimestamp) {
          oldestTimestamp = item.timestamp;
        }
      }
    }
    
    return {
      memorySize: this.syncQueue.size,
      persistentSize: Object.keys(persistedItems).length,
      oldestItem: oldestTimestamp
    };
  }
  
  // Enhanced batch sync with error recovery
  private processBatch = debounce(async () => {
    if (this.syncQueue.size === 0) {return;}
    
    const items = Array.from(this.syncQueue.values());
    
    // Log queue statistics before processing
    const stats = this.getQueueStats();
    console.log(`[Sync] Processing batch: ${stats.memorySize} items in memory, ${stats.persistentSize} in storage`);
    
    // Create a backup of items in case we need to restore them on failure
    const itemsBackup = new Map(this.syncQueue);
    
    // Clear memory queue (but keep persistent storage until successful completion)
    this.syncQueue.clear();
    
    const startTime = Date.now();
    
    try {
      const supabase = getSupabaseClient();
      
      // Batch upsert colors using optimized function
      const colors = items.filter(item => item.table === 'colors');
      if (colors.length > 0) {
        try {
          const { error } = await supabase.rpc('batch_upsert_colors', {
            p_user_id: this.userId,
            p_organization_id: this.organizationId,
            p_colors: colors.map(c => c.data)
          });
          
          if (error) {
            throw new Error(`Batch color sync failed: ${error.message}`);
          }
          
          console.log(`[Sync] Successfully processed ${colors.length} colors in batch`);
        } catch (error) {
          console.error('Batch color sync error:', error);
          
          // Queue colors for recovery
          this.queueForRecovery('batch processing', { items: colors });
          throw this.createUserFriendlyError(error, 'batch processing', { items: colors });
        }
      }
      
      // Batch upsert products
      const products = items.filter(item => item.table === 'products');
      if (products.length > 0) {
        try {
          const { error } = await supabase
            .from('products')
            .upsert(products.map(p => ({
              ...p.data,
              user_id: this.userId,
              organization_id: this.organizationId
            })), {
              onConflict: 'external_id'
            });
            
          if (error) {
            throw new Error(`Batch product sync failed: ${error.message}`);
          }
          
          console.log(`[Sync] Successfully processed ${products.length} products in batch`);
        } catch (error) {
          console.error('Batch product sync error:', error);
          
          // Queue products for recovery
          this.queueForRecovery('batch processing', { items: products });
          throw this.createUserFriendlyError(error, 'batch processing', { items: products });
        }
      }
      
      // Update sync metadata
      await this.updateSyncMetadata();
      
      // Track successful batch operation
      const duration = Date.now() - startTime;
      this.trackSyncPerformance('batch processing', duration, true);
      
      // Clear persistent storage on successful sync
      this.clearQueue();
      
      // Clear any previous errors on success
      this.clearLastError();
      
      console.log('[Sync] Batch processing completed successfully - cleared persistent queue');
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.trackSyncPerformance('batch processing', duration, false);
      
      console.error('Batch processing error:', error);
      
      // Restore items to memory queue for retry
      for (const [key, item] of itemsBackup.entries()) {
        this.syncQueue.set(key, item);
      }
      
      // Update persistent storage with restored items
      this.savePersistentQueue();
      
      console.log(`[Sync] Restored ${itemsBackup.size} items to queue for retry`);
      
      // If this was not already handled by individual operations, queue all items for recovery
      if (!error.message?.includes('Batch')) {
        this.queueForRecovery('batch processing', { items });
      }
    }
  }, 500);
  
  async initialize(organizationId: string) {
    try {
      // Input validation
      if (!organizationId || typeof organizationId !== 'string') {
        throw new Error('Invalid organization ID provided');
      }
      
      const supabase = getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User authentication required - please sign in to sync data');
      }
      
      this.userId = user.id;
      this.organizationId = organizationId;
      
      console.log(`[Sync] Initializing sync for user ${user.id} in organization ${organizationId}`);
    
      // Check sync limits before initializing
      const canSync = await this.checkSyncLimits();
      if (!canSync) {
        throw new Error('Monthly sync limit exceeded - upgrade to continue syncing');
      }
      
      // Ensure color sources are initialized before sync
      await this.ensureColorSources();
      
      // Ensure organization exists in local database before sync
      await this.ensureOrganizationExists();
      
      // Prepare local database for sync (add organization_id to existing data)
      await this.prepareLocalDataForSync();
      
      // Initialize sync metadata
      await this.initializeSyncMetadata();
      
      // Load persistent queue from previous session
      this.loadPersistentQueue();
      
      // Start network monitoring
      this.startNetworkMonitoring();
      
      // Subscribe to changes with optimized channel configuration
      await this.subscribeToChanges();
      
      // Start heartbeat monitoring for connection health
      this.startHeartbeat();
      
      // Start connection health monitoring
      this.startConnectionHealthMonitoring();
      
      // Perform initial sync
      await this.performInitialSync();
      
      this.isInitialized = true;
      console.log('[Sync] Initialization completed successfully');
      
    } catch (error) {
      await this.cleanup(); // Clean up any partial initialization
      
      // Don't queue initialization for recovery as it requires manual intervention
      const friendlyError = this.createUserFriendlyError(error, 'initialization');
      
      throw friendlyError;
    }
  }
  
  private async ensureColorSources() {
    const db = await getDatabase();
    
    try {
      // Check if color_sources table exists and has data
      const sourcesCount = db.prepare(`
        SELECT COUNT(*) as count FROM color_sources WHERE id = 1
      `).get()?.count || 0;
      
      if (sourcesCount === 0) {
        console.log('[Sync] Color sources not found, initializing...');
        
        // Insert the required color sources
        const insertStmt = db.prepare(`
          INSERT OR IGNORE INTO color_sources (id, code, name, is_system) VALUES (?, ?, ?, ?)
        `);
        
        const sources = [
          [1, 'user', 'User Created', false],
          [2, 'pantone', 'PANTONE®', true],
          [3, 'ral', 'RAL', true],
          [4, 'ncs', 'NCS', true]
        ];
        
        const transaction = db.transaction((sources) => {
          for (const source of sources) {
            insertStmt.run(...source);
          }
        });
        
        transaction(sources);
        console.log('[Sync] Color sources initialized successfully');
      } else {
        console.log('[Sync] Color sources already exist');
      }
    } catch (error) {
      console.error('[Sync] Error ensuring color sources:', error);
      throw error;
    }
  }
  
  private async ensureOrganizationExists() {
    const supabase = getSupabaseClient();
    const db = await getDatabase();
    
    try {
      // Check if organization exists in local database
      const existingOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (!existingOrg) {
        console.log('[Sync] Organization not found locally, fetching from Supabase...');
        
        // Fetch organization from Supabase
        const { data: orgData, error } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', this.organizationId)
          .single();
          
        if (error) {
          console.error('[Sync] Error fetching organization from Supabase:', error);
          throw new Error(`Organization ${this.organizationId} not found in Supabase`);
        }
        
        if (orgData) {
          // Generate a slug from the organization name
          const slug = orgData.name.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '')
            .slice(0, 50); // Limit length
          
          // Insert organization into local database
          const insertStmt = db.prepare(`
            INSERT OR REPLACE INTO organizations (external_id, name, slug, settings, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
          `);
          
          insertStmt.run(
            orgData.id,
            orgData.name,
            slug,
            JSON.stringify(orgData.settings || {}),
            orgData.created_at,
            orgData.updated_at
          );
          
          console.log(`[Sync] Successfully inserted organization ${orgData.name} into local database`);
        }
      } else {
        console.log('[Sync] Organization already exists in local database');
      }
    } catch (error) {
      console.error('[Sync] Error ensuring organization exists:', error);
      throw error;
    }
  }
  
  private async prepareLocalDataForSync() {
    const db = await getDatabase();
    
    try {
      // Check if organization_id column exists in colors table
      const hasOrgIdColumn = db.prepare(`
        SELECT COUNT(*) as count 
        FROM pragma_table_info('colors') 
        WHERE name = 'organization_id'
      `).get().count > 0;
      
      if (!hasOrgIdColumn) {
        console.log('[Sync] organization_id column should already exist from migration');
        throw new Error('Missing organization_id column - please run database migration');
      }
      
      // Get the local organization ID from the external organization ID
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (!localOrg) {
        console.error('[Sync] Organization not found when preparing local data for sync:', this.organizationId);
        throw new Error(`Organization ${this.organizationId} not found in local database`);
      }
      
      const localOrganizationId = localOrg.id;
      
      // Update existing colors with current organization ID if needed
      const updateStmt = db.prepare(`
        UPDATE colors 
        SET organization_id = ?, user_id = ? 
        WHERE (organization_id IS NULL OR organization_id = '') AND deleted_at IS NULL
      `);
      const result = updateStmt.run(localOrganizationId, this.userId);
      
      if (result.changes > 0) {
        console.log(`[Sync] Updated ${result.changes} existing colors with organization_id`);
      }
      
      // Verify the update worked
      const colorCount = db.prepare(`
        SELECT COUNT(*) as count FROM colors WHERE user_id = ? AND deleted_at IS NULL
      `).get(this.userId);
      console.log(`[Sync] Colors with user_id ${this.userId}: ${colorCount.count}`);
      
      // Do the same for products
      const hasProductUserIdColumn = db.prepare(`
        SELECT COUNT(*) as count 
        FROM pragma_table_info('products') 
        WHERE name = 'user_id'
      `).get().count > 0;
      
      if (!hasProductUserIdColumn) {
        console.log('[Sync] Adding user_id column to products table...');
        db.exec('ALTER TABLE products ADD COLUMN user_id TEXT');
      }
      
      const updateProductsStmt = db.prepare(`
        UPDATE products 
        SET user_id = ? 
        WHERE (user_id IS NULL OR user_id = '') AND is_active = 1
      `);
      const productResult = updateProductsStmt.run(this.userId);
      
      if (productResult.changes > 0) {
        console.log(`[Sync] Updated ${productResult.changes} existing products with user_id`);
      }
      
      // Verify the update worked
      const productCount = db.prepare(`
        SELECT COUNT(*) as count FROM products WHERE user_id = ? AND is_active = 1
      `).get(this.userId);
      console.log(`[Sync] Products with user_id ${this.userId}: ${productCount.count}`);
      
    } catch (error) {
      console.error('[Sync] Error preparing local data for sync:', error);
      // Don't throw here - sync can still work for new data
    }
  }
  
  private async checkSyncLimits(): Promise<boolean> {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('sync_metadata')
      .select('monthly_sync_count')
      .eq('user_id', this.userId)
      .single();
      
    if (error && error.code !== 'PGRST116') { // Not found is ok
      console.error('Error checking sync limits:', error);
      return true; // Allow sync on error
    }
    
    return !data || data.monthly_sync_count < 1000; // Free tier limit
  }
  
  private async initializeSyncMetadata() {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('sync_metadata')
      .upsert({
        user_id: this.userId,
        device_id: this.deviceId,
        last_sync: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });
      
    if (error) {console.error('Error initializing sync metadata:', error);}
  }
  
  private calculateBackoffDelay(attempt: number): number {
    // Exponential backoff with jitter: 2^attempt * 1000ms + random jitter
    const baseDelay = Math.pow(2, attempt) * 1000;
    const jitter = Math.random() * 1000; // Add up to 1s of random jitter
    const maxDelay = 30000; // Cap at 30 seconds
    
    return Math.min(baseDelay + jitter, maxDelay);
  }

  private async subscribeToChanges() {
    const supabase = getSupabaseClient();
    
    // Single channel for all organization data (optimized for free tier)
    this.channel = supabase
      .channel(`org-${this.organizationId}`)
      .on(
        'postgres_changes' as const,
        {
          event: '*',
          schema: 'public',
          table: 'products',
          filter: `organization_id=eq.${this.organizationId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleRealtimeChange('products', payload);
        }
      )
      .on(
        'postgres_changes' as const,
        {
          event: '*',
          schema: 'public',
          table: 'colors',
          filter: `organization_id=eq.${this.organizationId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleRealtimeChange('colors', payload);
        }
      )
      .subscribe((status) => {
        this.log('info', `[Sync] Realtime subscription status: ${status}`);
        this.handleSubscriptionStatus(status);
      });
      
    // Set up connection timeout monitoring
    this.setupConnectionTimeout();
      
    // Set up Phoenix protocol message handlers after subscription
    this.setupPhoenixMessageHandlers();
  }
  
  /**
   * Set up connection timeout monitoring based on Supabase recommendations
   */
  private setupConnectionTimeout() {
    // Clear existing timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
    }
    
    // Set up connection timeout
    this.connectionTimeout = setTimeout(() => {
      this.log('warn', '[Sync] Connection timeout reached - triggering reconnection');
      this.handleConnectionError();
    }, this.CONNECTION_TIMEOUT);
    
    this.log('debug', `[Sync] Connection timeout set for ${this.CONNECTION_TIMEOUT}ms`);
  }
  
  /**
   * Clear connection timeout (called on successful connection events)
   */
  private clearConnectionTimeout() {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
      this.log('debug', '[Sync] Connection timeout cleared');
    }
  }
  
  /**
   * Set up Phoenix protocol message handlers for proper protocol compliance
   */
  private setupPhoenixMessageHandlers() {
    if (!this.channel) {
      return;
    }
    
    // Handle Phoenix protocol replies (phx_reply)
    this.channel.on('phx_reply', {}, (payload: any) => {
      this.handlePhoenixReply(payload);
    });
    
    // Handle system messages for Postgres subscription status
    this.channel.on('system', {}, (payload: any) => {
      this.handleSystemMessage(payload);
    });
  }
  
  /**
   * Handle Phoenix protocol reply messages
   */
  private handlePhoenixReply(payload: any) {
    const { ref, response, status } = payload;
    
    if (ref && this.pendingRefs.has(ref)) {
      const pendingRef = this.pendingRefs.get(ref);
      this.pendingRefs.delete(ref);
      
      if (pendingRef?.type === 'heartbeat') {
        if (status === 'ok') {
          console.log(`[Sync] Heartbeat acknowledged (ref: ${ref})`);
        } else {
          console.warn(`[Sync] Heartbeat failed with status: ${status}`, response);
          this.handleHeartbeatFailure();
        }
      } else {
        console.log(`[Sync] Phoenix reply for ${pendingRef?.type || 'unknown'} (ref: ${ref}):`, { status, response });
      }
    }
  }
  
  /**
   * Handle system status messages for Postgres subscriptions
   */
  private handleSystemMessage(payload: any) {
    const { channel, extension, message, status } = payload;
    
    console.log(`[Sync] System message - Channel: ${channel}, Extension: ${extension}, Status: ${status}, Message: ${message}`);
    
    if (extension === 'postgres_changes') {
      if (status === 'ok' && message?.includes('Subscribed to PostgreSQL')) {
        console.log('[Sync] PostgreSQL subscription confirmed via system message');
      } else if (status === 'error' || message?.includes('failed')) {
        console.error('[Sync] PostgreSQL subscription error via system message:', message);
        this.handleConnectionError();
      }
    }
  }

  private handleSubscriptionStatus(status: string) {
    switch (status) {
      case 'SUBSCRIBED':
        this.log('info', '[Sync] Successfully subscribed to realtime changes');
        this.reconnectAttempts = 0; // Reset on successful connection
        this.isReconnecting = false;
        this.recordConnectionSuccess(); // Update circuit breaker state
        this.clearConnectionTimeout(); // Clear timeout on successful connection
        
        // Restart heartbeat if it's not running
        if (!this.heartbeatInterval) {
          this.startHeartbeat();
        }
        break;
        
      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
      case 'CLOSED':
        console.error(`[Sync] Channel status error: ${status}`);
        this.handleConnectionError();
        break;
        
      case 'CONNECTING':
        console.log('[Sync] Connecting to realtime...');
        break;
        
      default:
        console.log(`[Sync] Unknown status: ${status}`);
    }
  }

  private checkCircuitBreaker(): boolean {
    const now = Date.now();
    
    switch (this.circuitBreakerState) {
      case 'OPEN':
        if (now - this.lastFailureTime >= this.circuitBreakerTimeout) {
          console.log('[Sync] Circuit breaker moving to HALF_OPEN state');
          this.circuitBreakerState = 'HALF_OPEN';
          return true;
        }
        console.log('[Sync] Circuit breaker is OPEN, blocking connection attempt');
        return false;
        
      case 'HALF_OPEN':
      case 'CLOSED':
        return true;
        
      default:
        return false;
    }
  }

  private recordConnectionSuccess() {
    if (this.circuitBreakerState === 'HALF_OPEN') {
      console.log('[Sync] Circuit breaker moving to CLOSED state after successful connection');
      this.circuitBreakerState = 'CLOSED';
    }
    this.failureCount = 0;
  }

  private recordConnectionFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.maxFailures && this.circuitBreakerState === 'CLOSED') {
      console.log(`[Sync] Circuit breaker opening after ${this.failureCount} failures`);
      this.circuitBreakerState = 'OPEN';
    } else if (this.circuitBreakerState === 'HALF_OPEN') {
      console.log('[Sync] Circuit breaker reopening after failed test connection');
      this.circuitBreakerState = 'OPEN';
    }
  }

  private async checkNetworkConnectivity(): Promise<boolean> {
    return new Promise((resolve) => {
      const request = net.request({
        method: 'HEAD',
        url: 'https://supabase.com'
      });

      const timeout = setTimeout(() => {
        request.abort();
        resolve(false);
      }, 5000); // 5 second timeout

      request.on('response', () => {
        clearTimeout(timeout);
        resolve(true);
      });

      request.on('error', () => {
        clearTimeout(timeout);
        resolve(false);
      });

      request.end();
    });
  }

  private async monitorNetworkConnectivity() {
    const wasOnline = this.isOnline;
    this.isOnline = await this.checkNetworkConnectivity();
    
    if (!wasOnline && this.isOnline) {
      console.log('[Sync] Network connectivity restored');
      await this.handleNetworkReconnection();
    } else if (wasOnline && !this.isOnline) {
      console.log('[Sync] Network connectivity lost');
      this.handleNetworkDisconnection();
    }
  }

  private async handleNetworkReconnection() {
    // Reset circuit breaker when network comes back
    if (this.circuitBreakerState === 'OPEN') {
      console.log('[Sync] Resetting circuit breaker due to network reconnection');
      this.circuitBreakerState = 'CLOSED';
      this.failureCount = 0;
    }

    // Attempt to reconnect if we were waiting for network
    if (this.pendingReconnectOnOnline && !this.isReconnecting) {
      console.log('[Sync] Attempting reconnection after network restoration');
      this.pendingReconnectOnOnline = false;
      this.reconnectAttempts = 0; // Reset attempts when network comes back
      await this.handleConnectionError();
    }
  }

  private handleNetworkDisconnection() {
    // Mark that we should reconnect when network comes back
    this.pendingReconnectOnOnline = true;
    
    // Clean up current connection gracefully
    if (this.channel) {
      console.log('[Sync] Cleaning up connection due to network loss');
      this.channel.unsubscribe().catch((error) => {
        console.error('[Sync] Error unsubscribing during network loss:', error);
      });
      this.channel = null;
    }
  }

  private startNetworkMonitoring() {
    // Check network connectivity every 10 seconds
    this.networkCheckInterval = setInterval(() => {
      this.monitorNetworkConnectivity().catch((error) => {
        console.error('[Sync] Error monitoring network connectivity:', error);
      });
    }, 10000);
    
    // Initial check
    this.monitorNetworkConnectivity().catch((error) => {
      console.error('[Sync] Error in initial network check:', error);
    });
  }

  private startHeartbeat() {
    // Phoenix protocol typically uses 30-second heartbeat intervals
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, 30000);
    
    console.log('[Sync] Started heartbeat monitoring with 30s intervals');
  }

  private sendHeartbeat() {
    if (!this.channel || !this.isOnline) {
      return;
    }

    try {
      // Send proper Phoenix protocol heartbeat message
      console.log('[Sync] Sending Phoenix protocol heartbeat');
      
      const ref = this.sendPhoenixMessage('phoenix', 'heartbeat', {});
      
      if (ref) {
        console.log(`[Sync] Heartbeat sent with ref: ${ref}`);
        
        // Set a timeout to detect heartbeat failures
        setTimeout(() => {
          if (this.pendingRefs.has(ref)) {
            console.warn('[Sync] Heartbeat acknowledgment timeout');
            this.handleHeartbeatFailure();
          }
        }, 10000); // 10 second timeout for heartbeat acknowledgment
        
      } else {
        // Fallback to database query heartbeat if Phoenix message fails
        console.warn('[Sync] Phoenix heartbeat failed, falling back to database query');
        this.performHeartbeatQuery().catch((error) => {
          console.error('[Sync] Fallback heartbeat query failed:', error);
          this.handleHeartbeatFailure();
        });
      }

    } catch (error) {
      console.error('[Sync] Error sending heartbeat:', error);
      this.handleHeartbeatFailure();
    }
  }

  private async performHeartbeatQuery(): Promise<void> {
    const supabase = getSupabaseClient();
    
    // Perform a lightweight query to test connection health
    // This is more reliable than trying to send heartbeat messages directly
    const { error } = await supabase
      .from('sync_metadata')
      .select('user_id')
      .eq('user_id', this.userId)
      .limit(1);

    if (error) {
      throw new Error(`Heartbeat query failed: ${error.message}`);
    }

    console.log('[Sync] Heartbeat successful - connection healthy');
  }

  private handleHeartbeatFailure() {
    console.warn('[Sync] Heartbeat failed - connection may be unhealthy');
    
    // Record this as a connection failure for circuit breaker
    this.recordConnectionFailure();
    
    // If we're not already reconnecting, trigger a connection check
    if (!this.isReconnecting) {
      console.log('[Sync] Triggering connection health check due to heartbeat failure');
      this.handleConnectionError();
    }
  }

  private startConnectionHealthMonitoring() {
    // Check connection health every 2 minutes (less frequent than heartbeat)
    this.connectionHealthInterval = setInterval(() => {
      this.performConnectionHealthCheck().catch((error) => {
        console.error('[Sync] Error in connection health check:', error);
      });
    }, 120000); // 2 minutes
    
    console.log('[Sync] Started connection health monitoring with 2-minute intervals');
  }

  private async performConnectionHealthCheck(): Promise<void> {
    if (!this.isInitialized || !this.isOnline) {
      return; // Skip if not initialized or offline
    }

    const startTime = Date.now();
    this.lastHealthCheckTime = startTime;

    try {
      const supabase = getSupabaseClient();
      
      // Perform a very lightweight query to test connection health
      // This is different from heartbeat - it's specifically checking Supabase health
      const { error } = await supabase
        .from('organizations')
        .select('id')
        .eq('id', this.organizationId)
        .limit(1);

      if (error) {
        throw new Error(`Health check query failed: ${error.message}`);
      }

      const duration = Date.now() - startTime;
      console.log(`[Sync] Connection health check passed in ${duration}ms`);
      
      // Reset failure count on successful health check
      this.consecutiveHealthCheckFailures = 0;
      
      // Track if health check was slow
      if (duration > 5000) {
        console.warn(`[Sync] Slow health check: took ${duration}ms`);
      }

    } catch (error) {
      this.consecutiveHealthCheckFailures++;
      const duration = Date.now() - startTime;
      
      console.error(`[Sync] Health check failed (${this.consecutiveHealthCheckFailures}/${this.maxHealthCheckFailures}):`, error);
      
      // If we've had too many consecutive failures, attempt recovery
      if (this.consecutiveHealthCheckFailures >= this.maxHealthCheckFailures) {
        console.warn('[Sync] Multiple consecutive health check failures - attempting auto-recovery');
        this.autoRecoverConnection();
      }
    }
  }

  private async autoRecoverConnection(): Promise<void> {
    console.log('[Sync] Starting auto-recovery from unhealthy connection state');
    
    try {
      // Reset failure count
      this.consecutiveHealthCheckFailures = 0;
      
      // Clear all pending references to avoid memory leaks
      this.pendingRefs.clear();
      
      // Clean up current connection with proper error handling
      if (this.channel) {
        try {
          await this.channel.unsubscribe();
          console.log('[Sync] Successfully unsubscribed from channel during auto-recovery');
        } catch (unsubError) {
          console.warn('[Sync] Error during channel unsubscribe in auto-recovery:', unsubError);
        } finally {
          this.channel = null;
        }
      }
      
      // Stop and restart heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }
      
      // Reset circuit breaker if it was open
      if (this.circuitBreakerState === 'OPEN') {
        console.log('[Sync] Resetting circuit breaker during auto-recovery');
        this.circuitBreakerState = 'CLOSED';
        this.failureCount = 0;
      }
      
      // Reset reconnection state
      this.isReconnecting = false;
      this.reconnectAttempts = 0;
      
      // Refresh access token before reconnecting
      console.log('[Sync] Refreshing access token before reconnection');
      await this.refreshAccessToken();
      
      // Wait a moment before attempting recovery
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Re-establish connection with proper error handling
      try {
        await this.subscribeToChanges();
        this.startHeartbeat();
        console.log('[Sync] Auto-recovery completed successfully');
      } catch (reconnectError) {
        console.error('[Sync] Failed to re-establish connection during auto-recovery:', reconnectError);
        throw reconnectError;
      }
      
    } catch (error) {
      console.error('[Sync] Auto-recovery failed:', error);
      
      // Ensure clean state even if recovery fails
      this.channel = null;
      this.pendingRefs.clear();
      
      // Fall back to normal error handling
      this.handleConnectionError();
    }
  }

  private async handleConnectionError() {
    // Check network connectivity first
    if (!this.isOnline) {
      console.log('[Sync] Network is offline, deferring reconnection until network is restored');
      this.pendingReconnectOnOnline = true;
      return;
    }

    // Check circuit breaker before attempting reconnection
    if (!this.checkCircuitBreaker()) {
      return;
    }

    if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[Sync] Max reconnection attempts reached or already reconnecting');
      this.recordConnectionFailure();
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;
    
    const delay = this.calculateBackoffDelay(this.reconnectAttempts);
    console.log(`[Sync] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} after ${delay}ms`);
    
    // Clear existing timeout if any
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    
    this.reconnectTimeout = setTimeout(async () => {
      try {
        // Clean up existing channel
        if (this.channel) {
          await this.channel.unsubscribe();
          this.channel = null;
        }
        
        // Attempt to reconnect
        await this.subscribeToChanges();
        this.recordConnectionSuccess();
      } catch (error) {
        console.error('[Sync] Reconnection attempt failed:', error);
        this.isReconnecting = false;
        this.recordConnectionFailure();
        
        // Schedule next attempt if we haven't hit the limit and circuit breaker allows
        if (this.reconnectAttempts < this.maxReconnectAttempts && this.checkCircuitBreaker()) {
          this.handleConnectionError();
        } else {
          console.error('[Sync] All reconnection attempts exhausted or circuit breaker is open');
        }
      }
    }, delay);
  }
  
  private handleRealtimeChange(table: string, payload: RealtimePostgresChangesPayload<any>) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    // Skip if change originated from this device
    if (newRecord?.device_id === this.deviceId) {return;}
    
    const externalId = (newRecord as any)?.external_id || (oldRecord as any)?.external_id;
    
    // Validate UUID format before using it
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!externalId || !uuidRegex.test(externalId)) {
      console.error('[Sync] Malformed external_id in realtime change:', externalId, 'Table:', table, 'Event:', eventType);
      console.error('[Sync] Full payload for debugging:', { newRecord: newRecord ? { external_id: newRecord.external_id, id: newRecord.id } : null, oldRecord: oldRecord ? { external_id: oldRecord.external_id, id: oldRecord.id } : null });
      return; // Skip processing this change
    }
    
    const key = `${table}:${externalId}`;
    
    // Add to persistent queue with proper metadata
    if (eventType === 'DELETE') {
      this.addToQueue(key, { 
        table, 
        action: 'delete', 
        id: oldRecord.external_id,
        eventType,
        source: 'realtime'
      });
    } else {
      this.addToQueue(key, { 
        table, 
        action: 'upsert', 
        data: newRecord,
        eventType,
        source: 'realtime'
      });
    }
    
    // Process batch
    this.processBatch();
  }
  
  async performInitialSync() {
    if (this.isSyncing) {
      console.log('[Sync] Initial sync already in progress, skipping');
      return;
    }
    
    console.log('[Sync] Starting initial sync...');
    const startTime = Date.now();
    const supabase = getSupabaseClient();
    const db = await getDatabase();
    
    try {
      this.isSyncing = true;
      
      // Check if this is the first sync by checking if Supabase has any data for this organization
      // Add explicit filters to reduce RLS overhead, even though RLS policies exist
      const { count: remoteColorCount } = await supabase
        .from('colors')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', this.organizationId)
        .eq('user_id', this.userId); // Explicit user filter for RLS performance
        
      // Get local organization ID for local database queries
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (!localOrg) {
        console.error('[Sync] Organization not found during initial sync:', this.organizationId);
        throw new Error(`Organization ${this.organizationId} not found in local database`);
      }
      
      const localOrganizationId = localOrg.id;
      const localColorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL AND organization_id = ?').get(localOrganizationId).count;
      const isFirstSync = (remoteColorCount ?? 0) === 0 && localColorCount > 0;
      
      console.log(`[Sync] Local color count: ${localColorCount}, Remote color count: ${remoteColorCount || 0}, isFirstSync: ${isFirstSync}`);
      
      if (isFirstSync) {
        // First sync - push all local data to Supabase
        console.log('[Sync] First sync detected - pushing all local data to Supabase...');
        
        // Push local changes to remote
        await this.pushLocalChanges();
      } else if ((remoteColorCount ?? 0) > 0 && localColorCount === 0) {
        // Empty local database but remote has data - pull from Supabase
        console.log('[Sync] Local database empty but remote has data - pulling from Supabase...');
        
        // Optimized parallel queries with explicit filters for RLS performance
        const [productsResult, colorsResult] = await Promise.all([
          supabase
            .from('products')
            .select('*')
            .eq('organization_id', this.organizationId)
            .eq('user_id', this.userId), // Explicit user filter for RLS performance - removed limit for initial sync
          supabase
            .from('colors')
            .select('*')
            .eq('organization_id', this.organizationId)
            .eq('user_id', this.userId) // Explicit user filter for RLS performance - removed limit for initial sync
        ]);
        
        console.log(`[Sync] Fetched ${productsResult.data?.length || 0} products and ${colorsResult.data?.length || 0} colors from Supabase`);
        
        if (productsResult.error) {
          console.error('[Sync] Error fetching products from Supabase:', productsResult.error);
        }
        
        if (productsResult.data?.length === 0) {
          console.log('[Sync] ⚠️  NO PRODUCTS found in Supabase for organization:', this.organizationId);
          console.log('[Sync] This explains why no products are showing in the UI');
        }
        
        // Apply ALL remote data to local database with parallel batching
        if (productsResult.data && productsResult.data.length > 0) {
          console.log(`[Sync] Applying ${productsResult.data.length} products to local database...`);
          await this.applyProductsBatch(productsResult.data);
        }
        
        if (colorsResult.data && colorsResult.data.length > 0) {
          console.log(`[Sync] Applying ${colorsResult.data.length} colors to local database...`);
          await this.applyColorsBatch(colorsResult.data);
        }
        
        // Fetch and apply product-color relationships
        console.log('[Sync] Fetching product-color relationships from Supabase...');
        
        // First get product IDs for this organization with explicit filters
        const { data: orgProducts } = await supabase
          .from('products')
          .select('id, external_id')
          .eq('organization_id', this.organizationId)
          .eq('user_id', this.userId); // Explicit user filter for RLS performance
          
        if (orgProducts && orgProducts.length > 0) {
          const productIds = orgProducts.map(p => p.id); // Use internal ID, not external_id
          
          const { data: productColors } = await supabase
            .from('product_colors')
            .select('product_id, color_id, display_order, organization_id')
            .in('product_id', productIds)
            .eq('organization_id', this.organizationId);
            
          if (productColors && productColors.length > 0) {
            console.log(`[Sync] Applying ${productColors.length} product-color relationships...`);
            await this.applyProductColorsToLocal(productColors);
          }
        }
      } else {
        // Regular sync - check for updates since last sync
        // Add explicit organization filter for RLS performance
        const { data: syncMeta } = await supabase
          .from('sync_metadata')
          .select('last_sync')
          .eq('user_id', this.userId)
          .eq('organization_id', this.organizationId) // Explicit organization filter
          .single();
          
        const lastSync = syncMeta?.last_sync || '1970-01-01';
        console.log(`[Sync] Regular sync - checking for updates since ${lastSync}`);
        
        // Fetch remote changes since last sync
        console.log(`[Sync] Querying products updated after: ${lastSync}`);
        
        // Optimized parallel queries with explicit filters and date filtering
        const [productsResult, colorsResult] = await Promise.all([
          supabase
            .from('products')
            .select('*')
            .eq('organization_id', this.organizationId)
            .eq('user_id', this.userId) // Explicit user filter for RLS performance
            .gte('updated_at', lastSync), // Removed limit to get all updates
          supabase
            .from('colors')
            .select('*')
            .eq('organization_id', this.organizationId)
            .eq('user_id', this.userId) // Explicit user filter for RLS performance
            .gte('updated_at', lastSync) // Removed limit to get all updates
        ]);
        
        console.log(`[Sync] Regular sync fetched ${productsResult.data?.length || 0} products updated since ${lastSync}`);
        
        if (productsResult.error) {
          console.error('[Sync] Error fetching products in regular sync:', productsResult.error);
        }
        
        // DEBUG: Check what users/products exist in Supabase for this organization
        console.log('[Sync] DEBUG: Checking all users and products in Supabase for organization:', this.organizationId);
        const { data: allOrgProducts } = await supabase
          .from('products')
          .select('user_id, name')
          .eq('organization_id', this.organizationId);
        console.log('[Sync] DEBUG: All products in organization:', allOrgProducts);
        console.log('[Sync] DEBUG: Current user_id:', this.userId);
        
        // Check local product count
        const localProductCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = TRUE AND organization_id = ?').get(localOrganizationId).count;
        
        // If no products found and this looks like first sync, do a full product fetch
        if ((!productsResult.data || productsResult.data.length === 0) && localProductCount === 0) {
          console.log('[Sync] No updated products found and local DB seems empty - doing full product fetch...');
          
          const { data: allProducts, error: allProductsError } = await supabase
            .from('products')
            .select('*')
            .eq('organization_id', this.organizationId)
            .eq('user_id', this.userId); // Explicit user filter for RLS performance - removed limit for full fetch
            
          if (allProductsError) {
            console.error('[Sync] Error fetching all products:', allProductsError);
          } else {
            console.log(`[Sync] Full fetch found ${allProducts?.length || 0} total products in Supabase`);
            if (allProducts && allProducts.length > 0) {
              console.log(`[Sync] Applying ${allProducts.length} products from full fetch...`);
              await this.applyProductsBatch(allProducts);
            } else {
              console.log('[Sync] ⚠️  NO PRODUCTS exist in Supabase for this organization');
              console.log('[Sync] This explains why the UI shows 0 products');
            }
          }
        } else {
          // Apply remote changes to local database with parallel batching
          if (productsResult.data && productsResult.data.length > 0) {
            await this.applyProductsBatch(productsResult.data);
          }
        }
        
        if (colorsResult.data && colorsResult.data.length > 0) {
          await this.applyColorsBatch(colorsResult.data);
        }
        
        // Fetch and apply product-color relationships
        // Note: product_colors doesn't have updated_at, so we fetch all for now
        console.log('[Sync] Fetching product-color relationships from Supabase...');
        
        // First get product IDs for this organization with explicit filters
        const { data: orgProducts } = await supabase
          .from('products')
          .select('id, external_id')
          .eq('organization_id', this.organizationId)
          .eq('user_id', this.userId); // Explicit user filter for RLS performance
          
        if (orgProducts && orgProducts.length > 0) {
          const productIds = orgProducts.map(p => p.id); // Use internal ID, not external_id
          
          const { data: productColors } = await supabase
            .from('product_colors')
            .select('product_id, color_id, display_order, organization_id')
            .in('product_id', productIds)
            .eq('organization_id', this.organizationId);
            
          if (productColors && productColors.length > 0) {
            console.log(`[Sync] Applying ${productColors.length} product-color relationships...`);
            await this.applyProductColorsToLocal(productColors);
          }
        }
        
        // Push local changes to remote
        await this.pushLocalChanges();
      }
      
      // Update sync metadata
      await this.updateSyncMetadata();
      
      const duration = Date.now() - startTime;
      this.trackSyncPerformance('initial sync', duration, true);
      console.log(`[Sync] Initial sync completed successfully in ${duration}ms`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.trackSyncPerformance('initial sync', duration, false);
      
      // Queue initial sync for recovery if it's a recoverable error
      const friendlyError = this.createUserFriendlyError(error, 'initial sync', { organizationId: this.organizationId });
      
      throw friendlyError;
    } finally {
      this.isSyncing = false;
    }
  }
  
  private async applyProductToLocal(product: any) {
    const db = await getDatabase();
    
    try {
      // Data validation
      if (!product) {
        console.error('[Sync] Product data is null or undefined');
        return;
      }
      
      if (!product.external_id) {
        console.error('[Sync] Product missing required external_id:', product);
        return;
      }
      
      // Validate UUID format for external_id
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(product.external_id)) {
        console.error('[Sync] Product has malformed external_id:', product.external_id, product);
        return;
      }
      
      if (!product.name || typeof product.name !== 'string') {
        console.error('[Sync] Product missing or invalid name:', product);
        return;
      }
      
      // Check if organization_id column exists in products table
      const hasOrgIdColumn = db.prepare(`
        SELECT COUNT(*) as count
        FROM pragma_table_info('products')
        WHERE name = 'organization_id'
      `).get().count > 0;
      
      if (!hasOrgIdColumn) {
        console.log('[Sync] organization_id column should already exist from migration');
        throw new Error('Missing organization_id column - please run database migration');
      }
      
      // Get the local organization ID from the external organization ID
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(product.organization_id || this.organizationId);
      
      if (!localOrg) {
        console.error('[Sync] Organization not found for product:', product.organization_id || this.organizationId);
        return;
      }
      
      // Use the local integer organization ID, not the UUID
      const localOrganizationId = localOrg.id;
      
      // Safely handle metadata serialization
      let metadataStr;
      try {
        // Ensure metadata is an object before spreading
        const baseMetadata = typeof product.metadata === 'object' && product.metadata !== null 
          ? product.metadata 
          : {};
        
        metadataStr = JSON.stringify({
          ...baseMetadata, 
          sku: product.sku
        });
      } catch (metadataError) {
        console.error('[Sync] Failed to serialize product metadata:', metadataError, product.metadata);
        metadataStr = JSON.stringify({ sku: product.sku });
      }
      
      // Convert and apply to local database
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO products (external_id, name, description, metadata, organization_id, user_id, created_by, updated_at, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        product.external_id,
        product.name,
        product.description || product.sku || '',
        metadataStr,
        localOrganizationId, // Use local integer ID, not UUID
        product.user_id || this.userId,
        product.created_by || product.user_id || this.userId || null,
        product.updated_at || new Date().toISOString(),
        1 // is_active = true (use integer instead of boolean)
      );
      
      console.log(`[Sync] Successfully applied product ${product.name} to local database`);
    } catch (error) {
      console.error('[Sync] Error applying product to local:', error, product);
    }
  }
  
  private async applyColorToLocal(color: any) {
    const db = await getDatabase();
    
    try {
      // Comprehensive data validation
      if (!color) {
        console.error('[Sync] Color data is null or undefined');
        return;
      }
      
      if (!color.external_id) {
        console.error('[Sync] Color missing required external_id:', color);
        return;
      }
      
      // Validate UUID format for external_id
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(color.external_id)) {
        console.error('[Sync] Color has malformed external_id:', color.external_id, color);
        return;
      }
      
      if (!color.hex || !/^#[0-9A-Fa-f]{6}$/.test(color.hex)) {
        console.error('[Sync] Color has invalid hex value:', color.hex);
        return;
      }
      
      if (!color.code) {
        console.error('[Sync] Color missing required code:', color);
        return;
      }
      
      // Validate organization ID format if present
      if (color.organization_id && !uuidRegex.test(color.organization_id)) {
        console.error('[Sync] Color has malformed organization_id:', color.organization_id, color);
        return;
      }
      
      // Get the local organization ID from the external organization ID
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(color.organization_id || this.organizationId);
      
      if (!localOrg) {
        console.error('[Sync] Organization not found for color:', color.organization_id || this.organizationId);
        return;
      }
      
      const localOrganizationId = localOrg.id;
      
      // Safely handle properties serialization
      let propertiesStr;
      try {
        // Ensure properties is an object before serializing
        const baseProperties = typeof color.properties === 'object' && color.properties !== null 
          ? color.properties 
          : {};
        
        propertiesStr = JSON.stringify(baseProperties);
      } catch (propertiesError) {
        console.error('[Sync] Failed to serialize color properties:', propertiesError, color.properties);
        propertiesStr = JSON.stringify({});
      }
      
      // Check if color already exists
      const existing = db.prepare(`
        SELECT id FROM colors WHERE external_id = ?
      `).get(color.external_id);
      
      let colorId;
      
      if (existing) {
        // Update existing color
        const updateStmt = db.prepare(`
          UPDATE colors 
          SET source_id = ?, code = ?, name = ?, display_name = ?, hex = ?, 
              properties = ?, organization_id = ?, user_id = ?, updated_at = ?
          WHERE external_id = ?
        `);
        
        updateStmt.run(
          color.source_id,
          color.code,
          color.display_name || color.code || 'Unnamed Color',
          color.display_name || color.code,
          color.hex,
          propertiesStr,
          localOrganizationId,
          color.user_id || this.userId,
          color.updated_at || new Date().toISOString(),
          color.external_id
        );
        
        colorId = existing.id;
      } else {
        // Insert new color
        const insertStmt = db.prepare(`
          INSERT INTO colors (external_id, source_id, code, name, display_name, hex, properties, organization_id, user_id, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const result = insertStmt.run(
          color.external_id,
          color.source_id,
          color.code,
          color.display_name || color.code || 'Unnamed Color',
          color.display_name || color.code,
          color.hex,
          propertiesStr,
          localOrganizationId,
          color.user_id || this.userId,
          color.updated_at || new Date().toISOString()
        );
        
        colorId = result.lastInsertRowid;
      }
      
      // Apply color spaces if present
      if (color.color_spaces && colorId) {
        const spaces = color.color_spaces;
        
        // Only sync CMYK - other color spaces are calculated from hex
        if (spaces.cmyk) {
          const cmykStmt = db.prepare(`
            INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
            VALUES (?, ?, ?, ?, ?)
          `);
          cmykStmt.run(colorId, spaces.cmyk.c, spaces.cmyk.m, spaces.cmyk.y, spaces.cmyk.k);
        }
        
        // RGB, LAB, and HSL are calculated from hex in the app, so we don't sync them
      }
      
      console.log(`[Sync] Successfully applied color ${color.code} to local database`);
    } catch (error) {
      console.error('[Sync] Error applying color to local:', error, color);
    }
  }

  private async applyProductsBatch(products: any[], batchSize?: number): Promise<void> {
    const startTime = Date.now();
    const effectiveBatchSize = batchSize ?? this.batchSizes.products;
    const batches = [];
    for (let i = 0; i < products.length; i += effectiveBatchSize) {
      batches.push(products.slice(i, i + effectiveBatchSize));
    }

    console.log(`[Sync] Processing ${products.length} products in ${batches.length} parallel batches of ${effectiveBatchSize}`);
    
    let processedCount = 0;
    for (const batch of batches) {
      const batchPromises = batch.map(product => this.applyProductToLocal(product));
      await Promise.all(batchPromises);
      processedCount += batch.length;
      console.log(`[Sync] Products batch progress: ${processedCount}/${products.length}`);
    }
    
    const duration = Date.now() - startTime;
    console.log(`[Sync] Successfully processed all ${products.length} products in ${duration}ms`);
    
    if (duration > 5000) {
      console.warn(`[Sync] Slow batch operation: processing ${products.length} products took ${duration}ms`);
    }
  }

  private async applyColorsBatch(colors: any[], batchSize?: number): Promise<void> {
    const startTime = Date.now();
    const effectiveBatchSize = batchSize ?? this.batchSizes.colors;
    const batches = [];
    for (let i = 0; i < colors.length; i += effectiveBatchSize) {
      batches.push(colors.slice(i, i + effectiveBatchSize));
    }

    console.log(`[Sync] Processing ${colors.length} colors in ${batches.length} parallel batches of ${effectiveBatchSize}`);
    
    let processedCount = 0;
    for (const batch of batches) {
      const batchPromises = batch.map(color => this.applyColorToLocal(color));
      await Promise.all(batchPromises);
      processedCount += batch.length;
      console.log(`[Sync] Colors batch progress: ${processedCount}/${colors.length}`);
    }
    
    const duration = Date.now() - startTime;
    console.log(`[Sync] Successfully processed all ${colors.length} colors in ${duration}ms`);
    
    if (duration > 5000) {
      console.warn(`[Sync] Slow batch operation: processing ${colors.length} colors took ${duration}ms`);
    }
  }
  
  private async applyProductColorsToLocal(productColors: any[], batchSize?: number): Promise<void> {
    const effectiveBatchSize = batchSize ?? this.batchSizes.relationships;
    if (productColors.length === 0) {return;}
    
    const db = await getDatabase();
    
    // Get the local organization ID
    const localOrg = db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(this.organizationId);
    
    if (!localOrg) {
      throw new Error(`Organization not found for product-color relationships: ${this.organizationId}`);
    }
    
    const localOrganizationId = localOrg.id;
    
    try {
      console.log(`[Sync] Optimizing ${productColors.length} product-color relationships with batch processing`);
      
      // Get unique product and color IDs from the relationships
      const uniqueProductIds = [...new Set(productColors.map(pc => pc.product_id))];
      const uniqueColorIds = [...new Set(productColors.map(pc => pc.color_id))];
      
      console.log(`[Sync] Mapping ${uniqueProductIds.length} products and ${uniqueColorIds.length} colors`);
      
      // Batch fetch Supabase IDs to external IDs mapping (parallel queries)
      const supabase = getSupabaseClient();
      const [productsResult, colorsResult] = await Promise.all([
        supabase
          .from('products')
          .select('id, external_id')
          .in('id', uniqueProductIds),
        supabase
          .from('colors')
          .select('id, external_id')
          .in('id', uniqueColorIds)
      ]);
      
      if (productsResult.error || colorsResult.error) {
        throw new Error(`Failed to fetch ID mappings: ${productsResult.error?.message || colorsResult.error?.message}`);
      }
      
      // Batch lookup local IDs using IN queries (more efficient than individual lookups)
      const productExternalIds = productsResult.data?.map(p => p.external_id) || [];
      const colorExternalIds = colorsResult.data?.map(c => c.external_id) || [];
      
      // Efficient batch lookups for local IDs
      const localProducts = productExternalIds.length > 0 
        ? db.prepare(`SELECT id, external_id FROM products WHERE external_id IN (${productExternalIds.map(() => '?').join(',')})`).all(...productExternalIds)
        : [];
      
      const localColors = colorExternalIds.length > 0
        ? db.prepare(`SELECT id, external_id FROM colors WHERE external_id IN (${colorExternalIds.map(() => '?').join(',')})`).all(...colorExternalIds)
        : [];
      
      // Create efficient lookup maps
      const productIdMap = new Map<number, number>();
      const colorIdMap = new Map<number, number>();
      
      // Build product ID mapping (Supabase ID -> Local ID)
      for (const supProd of productsResult.data || []) {
        const localProd = localProducts.find((lp: any) => lp.external_id === supProd.external_id);
        if (localProd) {
          productIdMap.set(supProd.id, localProd.id);
        }
      }
      
      // Build color ID mapping (Supabase ID -> Local ID)
      for (const supColor of colorsResult.data || []) {
        const localColor = localColors.find((lc: any) => lc.external_id === supColor.external_id);
        if (localColor) {
          colorIdMap.set(supColor.id, localColor.id);
        }
      }
      
      // Filter and prepare valid relationships
      const validRelationships = productColors
        .map(pc => ({
          localProductId: productIdMap.get(pc.product_id),
          localColorId: colorIdMap.get(pc.color_id),
          displayOrder: pc.display_order || 0
        }))
        .filter(rel => rel.localProductId && rel.localColorId);
      
      console.log(`[Sync] Found ${validRelationships.length} valid relationships out of ${productColors.length} total`);
      
      if (validRelationships.length === 0) {
        console.log('[Sync] No valid relationships to apply');
        return;
      }
      
      // Apply relationships in batches using transaction for better performance
      const batches = [];
      for (let i = 0; i < validRelationships.length; i += effectiveBatchSize) {
        batches.push(validRelationships.slice(i, i + effectiveBatchSize));
      }
      
      console.log(`[Sync] Processing relationships in ${batches.length} batches of ${effectiveBatchSize}`);
      
      const insertStmt = db.prepare(`
        INSERT OR REPLACE INTO product_colors (product_id, color_id, display_order, organization_id)
        VALUES (?, ?, ?, ?)
      `);
      
      let totalApplied = 0;
      for (const [index, batch] of batches.entries()) {
        // Use transaction for each batch for better performance and consistency
        const transaction = db.transaction((relationships: typeof validRelationships) => {
          for (const rel of relationships) {
            insertStmt.run(rel.localProductId, rel.localColorId, rel.displayOrder, localOrganizationId);
          }
        });
        
        transaction(batch);
        totalApplied += batch.length;
        
        console.log(`[Sync] Applied batch ${index + 1}/${batches.length}: ${totalApplied}/${validRelationships.length} relationships`);
      }
      
      console.log(`[Sync] Successfully applied ${totalApplied} product-color relationships using optimized batch processing`);
      
    } catch (error) {
      console.error('[Sync] Error applying product-color relationships to local:', error);
      throw error; // Re-throw to allow higher-level error handling
    }
  }
  
  private async pushLocalChanges() {
    console.log('[Sync] Starting push of local changes...');
    
    const startTime = Date.now();
    
    try {
      const db = await getDatabase();
      const supabase = getSupabaseClient();
      
      // Validate required services are available
      if (!db) {
        throw new Error('Database not available for sync operation');
      }
      
      if (!supabase) {
        throw new Error('Supabase client not available for sync operation');
      }
      
      if (!this.organizationId || !this.userId) {
        throw new Error('Organization ID and User ID required for sync operation');
      }
    
    // Get local organization ID for database queries
    let localOrg;
    let localOrganizationId;
    try {
      localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (!localOrg) {
        console.error('[Sync] Organization not found when pushing local changes:', this.organizationId);
        throw new Error(`Organization ${this.organizationId} not found in local database`);
      }
      
      localOrganizationId = localOrg.id;
    } catch (error) {
      console.error('[Sync] Database error getting organization ID:', error);
      this.queueForRecovery('organization lookup', { organizationId: this.organizationId });
      throw this.createUserFriendlyError(error, 'organization lookup');
    }
    
    // Get local changes - include colors even without organization_id for initial sync
    let products, colors;
    try {
      products = db.prepare(`
        SELECT * FROM products WHERE is_active = 1 AND (organization_id = ? OR organization_id IS NULL)
      `).all(localOrganizationId);
      
      colors = db.prepare(`
        SELECT * FROM colors WHERE deleted_at IS NULL AND (organization_id = ? OR organization_id IS NULL)
      `).all(localOrganizationId);
      
      console.log(`[Sync] Found ${products.length} products and ${colors.length} colors to sync`);
    } catch (error) {
      console.error('[Sync] Database error fetching local changes:', error);
      this.queueForRecovery('data fetching', { localOrganizationId });
      throw this.createUserFriendlyError(error, 'data fetching');
    }
    
    // Batch push changes
    if (products.length > 0) {
      try {
        console.log(`[Sync] Pushing ${products.length} products to Supabase...`);
        const { error } = await supabase
          .from('products')
          .upsert(products.map((p: any) => ({
            external_id: p.external_id,
            organization_id: this.organizationId,
            user_id: this.userId,
            name: p.name,
            sku: p.sku,
            metadata: JSON.parse(p.metadata || '{}'),
            device_id: this.deviceId
          })), {
            onConflict: 'external_id'
          });
          
        if (error) {
          console.error('[Sync] Push products error:', error);
          this.queueForRecovery('product sync', { products: products.map(p => p.external_id) });
          throw this.createUserFriendlyError(error, 'product sync', { count: products.length });
        } else {
          console.log(`[Sync] Successfully pushed ${products.length} products`);
        }
      } catch (error) {
        console.error('[Sync] Error during product sync:', error);
        this.queueForRecovery('product sync', { products: products.map(p => p.external_id) });
        throw this.createUserFriendlyError(error, 'product sync', { count: products.length });
      }
    }
    
    if (colors.length > 0) {
      try {
        console.log(`[Sync] Pushing ${colors.length} local colors to Supabase...`);
        
        // Get CMYK values only (RGB, LAB, HSL can be calculated from hex)
        const colorSpaces = new Map();
        for (const color of colors) {
          try {
            const spaces: any = {};
            
            // Only get CMYK as it can't be accurately derived from hex without color profiles
            const cmyk = db.prepare('SELECT c, m, y, k FROM color_cmyk WHERE color_id = ?').get(color.id);
            if (cmyk) {spaces.cmyk = cmyk;}
            
            colorSpaces.set(color.id, spaces);
          } catch (error) {
            console.warn(`[Sync] Failed to get color spaces for color ${color.id}:`, error);
            // Continue with other colors even if one fails
            colorSpaces.set(color.id, {});
          }
        }
        
        // Convert colors to Supabase format with UUID validation and deduplication
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        
        // Deduplicate colors by user_id, source_id, and code (the unique constraint)
        const uniqueColorsMap = new Map();
        colors.forEach((c: any) => {
          const key = `${c.user_id || this.userId}:${c.source_id}:${c.code}`;
          if (!uniqueColorsMap.has(key)) {
            uniqueColorsMap.set(key, c);
          } else {
            console.warn(`[Sync] Skipping duplicate color: ${c.code} (user: ${c.user_id}, source: ${c.source_id})`);
          }
        });
        const uniqueColors = Array.from(uniqueColorsMap.values());
        
        console.log(`[Sync] Deduplicated colors: ${colors.length} -> ${uniqueColors.length}`);
        
        const colorData = uniqueColors.map((c: any) => {
          try {
            // Validate and fix malformed external_id
            if (!c.external_id || !uuidRegex.test(c.external_id)) {
              console.warn(`[Sync] Color has malformed external_id: ${c.external_id}, generating new UUID`);
              // Generate a new UUID for malformed ones
              c.external_id = crypto.randomUUID();
              
              // Update the local database with the new UUID
              try {
                db.prepare('UPDATE colors SET external_id = ? WHERE id = ?').run(c.external_id, c.id);
                console.log(`[Sync] Updated malformed UUID for color ${c.id} to ${c.external_id}`);
              } catch (updateError) {
                console.error(`[Sync] Failed to update malformed UUID for color ${c.id}:`, updateError);
              }
            }
            
            const props = JSON.parse(c.properties || '{}');
            
            // Move local-only fields into properties JSONB
            if (c.is_gradient) {props.is_gradient = c.is_gradient;}
            if (c.is_metallic) {props.is_metallic = c.is_metallic;}
            if (c.is_effect) {props.is_effect = c.is_effect;}
            if (c.search_terms) {props.search_terms = c.search_terms;}
            if (c.notes) {props.notes = c.notes;}
            
            return {
              external_id: c.external_id,
              organization_id: this.organizationId,
              user_id: this.userId,
              source_id: c.source_id || 1,
              code: c.code,
              display_name: c.display_name || c.code,
              hex: c.hex,
              color_spaces: colorSpaces.get(c.id) || {},
              properties: props,
              device_id: this.deviceId
            };
          } catch (error) {
            console.warn(`[Sync] Failed to process color ${c.id}:`, error);
            
            // Generate a new UUID for failed processing
            const fixedExternalId = !c.external_id || !uuidRegex.test(c.external_id) ? 
              crypto.randomUUID() : c.external_id;
            
            // Return a minimal valid object for failing colors
            return {
              external_id: fixedExternalId,
              organization_id: this.organizationId,
              user_id: this.userId,
              source_id: c.source_id || 1,
              code: c.code || 'Unknown',
              display_name: c.display_name || c.code || 'Unknown',
              hex: c.hex || '#000000',
              color_spaces: {},
              properties: {},
              device_id: this.deviceId
            };
          }
        });
        
        // Use direct upsert instead of RPC for now
        console.log(`[Sync] Sample color data:`, JSON.stringify(colorData[0], null, 2));
        
        const { data, error } = await supabase
          .from('colors')
          .upsert(colorData, {
            onConflict: 'user_id,source_id,code'
          })
          .select();
        
        if (error) {
          console.error('[Sync] Push colors error:', error);
          this.queueForRecovery('color sync', { colors: colors.map(c => c.external_id) });
          throw this.createUserFriendlyError(error, 'color sync', { count: uniqueColors.length });
        } else {
          console.log(`[Sync] Successfully pushed ${uniqueColors.length} colors`);
          console.log(`[Sync] Supabase returned ${data?.length || 0} records`);
          
          // Verify data actually exists in Supabase
          try {
            const { count } = await supabase
              .from('colors')
              .select('*', { count: 'exact', head: true })
              .eq('organization_id', this.organizationId);
            
            console.log(`[Sync] Total colors in Supabase for organization: ${count || 0}`);
          } catch (verifyError) {
            console.warn('[Sync] Failed to verify color count in Supabase:', verifyError);
            // Don't fail the sync for verification errors
          }
        }
      } catch (error) {
        console.error('[Sync] Error during color sync:', error);
        this.queueForRecovery('color sync', { colors: colors.map(c => c.external_id) });
        throw this.createUserFriendlyError(error, 'color sync', { count: colors.length });
      }
    }
    
    // Push product-color relationships
    console.log('[Sync] Pushing product-color relationships...');
    console.log('[Sync] Current user ID:', this.userId);
    
    // First check if product_colors table exists in Supabase
    const { error: tableError } = await supabase
      .from('product_colors')
      .select('*')
      .limit(1);
    
    if (tableError && tableError.message.includes('relation')) {
      console.error('[Sync] ERROR: product_colors table does not exist in Supabase!');
      console.error('[Sync] Please run the fix-supabase-product-colors.sql script in Supabase SQL Editor');
      return;
    }
    
    try {
      // First, let's check what we have locally
      const totalRelations = db.prepare(`
        SELECT COUNT(*) as count FROM product_colors
      `).get() as { count: number };
      console.log(`[Sync] Total product-color relationships in local DB: ${totalRelations.count}`);
      
      // Get all product-color relationships where both product and color have been synced
      const productColorRelations = db.prepare(`
        SELECT 
          p.external_id as product_external_id,
          c.external_id as color_external_id,
          pc.display_order
        FROM product_colors pc
        JOIN products p ON pc.product_id = p.id
        JOIN colors c ON pc.color_id = c.id
        WHERE p.organization_id = ? 
          AND c.organization_id = ?
          AND p.is_active = 1
          AND c.deleted_at IS NULL
      `).all(localOrganizationId, localOrganizationId) as Array<{
        product_external_id: string;
        color_external_id: string;
        display_order: number;
      }>;
      
      console.log(`[Sync] Found ${productColorRelations.length} product-color relationships for user ${this.userId}`);
      
      if (productColorRelations.length > 0) {
        console.log(`[Sync] Sample relationships:`, productColorRelations.slice(0, 3));
        
        // First, get the Supabase IDs for products and colors
        const productExternalIds = [...new Set(productColorRelations.map(r => r.product_external_id))];
        const colorExternalIds = [...new Set(productColorRelations.map(r => r.color_external_id))];
        
        console.log(`[Sync] Fetching Supabase IDs for ${productExternalIds.length} products and ${colorExternalIds.length} colors`);
        
        // Fetch Supabase IDs for products
        const { data: supabaseProducts, error: productsError } = await supabase
          .from('products')
          .select('id, external_id')
          .in('external_id', productExternalIds);
          
        if (productsError) {
          console.error('[Sync] Error fetching Supabase product IDs:', productsError);
        } else {
          console.log(`[Sync] Found ${supabaseProducts?.length || 0} products in Supabase`);
        }
        
        // Fetch Supabase IDs for colors in batches (to avoid query size limits)
        const colorBatchSize = this.batchSizes.supabaseQuery; // Use configurable batch size
        const supabaseColors: any[] = [];
        
        for (let i = 0; i < colorExternalIds.length; i += colorBatchSize) {
          const batch = colorExternalIds.slice(i, i + colorBatchSize);
          console.log(`[Sync] Fetching color batch ${Math.floor(i / colorBatchSize) + 1}/${Math.ceil(colorExternalIds.length / colorBatchSize)}`);
          
          const { data: batchColors, error: colorsError } = await supabase
            .from('colors')
            .select('id, external_id')
            .in('external_id', batch);
            
          if (colorsError) {
            console.error('[Sync] Error fetching Supabase color IDs:', colorsError);
            break;
          } else if (batchColors) {
            supabaseColors.push(...batchColors);
          }
        }
        
        console.log(`[Sync] Found ${supabaseColors.length} colors in Supabase`);
        
        if (supabaseProducts && supabaseColors.length > 0) {
          // Create lookup maps
          const productIdMap = new Map(supabaseProducts.map((p: any) => [p.external_id, p.id]));
          const colorIdMap = new Map(supabaseColors.map((c: any) => [c.external_id, c.id]));
          
          console.log(`[Sync] Product ID map size: ${productIdMap.size}, Color ID map size: ${colorIdMap.size}`);
          
          // Prepare product_colors data for Supabase
          const unmappedRelations = productColorRelations.filter(r => 
            !productIdMap.has(r.product_external_id) || !colorIdMap.has(r.color_external_id)
          );
          
          if (unmappedRelations.length > 0) {
            console.log(`[Sync] WARNING: ${unmappedRelations.length} relationships could not be mapped`);
            console.log(`[Sync] Sample unmapped:`, unmappedRelations.slice(0, 3));
          }
          
          const productColorsData = productColorRelations
            .filter(r => productIdMap.has(r.product_external_id) && colorIdMap.has(r.color_external_id))
            .map(r => ({
              product_id: productIdMap.get(r.product_external_id),
              color_id: colorIdMap.get(r.color_external_id),
              display_order: r.display_order || 0,
              organization_id: this.organizationId
            }));
          
          console.log(`[Sync] Prepared ${productColorsData.length} relationships for sync`);
          if (productColorsData.length > 0) {
            console.log(`[Sync] Sample data to sync:`, productColorsData.slice(0, 3));
            console.log(`[Sync] Pushing ${productColorsData.length} product-color relationships to Supabase...`);
            
            // Push to Supabase
            const { data: upsertedData, error: relationError } = await supabase
              .from('product_colors')
              .upsert(productColorsData, {
                onConflict: 'product_id,color_id'
              })
              .select();
              
            if (relationError) {
              console.error('[Sync] Error pushing product-color relationships:', relationError);
              console.error('[Sync] Error details:', JSON.stringify(relationError, null, 2));
            } else {
              console.log(`[Sync] Successfully pushed ${productColorsData.length} product-color relationships`);
              console.log(`[Sync] Upserted data count:`, upsertedData?.length || 0);
            }
          }
        }
      }
    } catch (error) {
      console.error('[Sync] Error syncing product-color relationships:', error);
      this.queueForRecovery('product-color relationships', { organizationId: this.organizationId });
      // Don't throw here - this is a non-critical operation
    }
    
    // Track successful completion
    const duration = Date.now() - startTime;
    this.trackSyncPerformance('push local changes', duration, true);
    
    // Clear any previous errors on success
    this.clearLastError();
    
    console.log('[Sync] Push completed successfully');
    
    } catch (error) {
      const duration = Date.now() - startTime;
      this.trackSyncPerformance('push local changes', duration, false);
      
      console.error('[Sync] Critical error during pushLocalChanges:', error);
      
      // Track error in history
      this.trackError(error, 'pushLocalChanges', { 
        organizationId: this.organizationId,
        userId: this.userId 
      });
      
      // Queue entire operation for recovery if not already queued by specific operations
      if (!error.message?.includes('sync')) {
        this.queueForRecovery('complete push operation', { 
          organizationId: this.organizationId,
          userId: this.userId,
          timestamp: startTime
        });
      }
      
      // Re-throw as user-friendly error
      throw this.createUserFriendlyError(error, 'sync operation');
    }
  }
  
  private async updateSyncMetadata() {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('sync_metadata')
      .update({
        last_sync: new Date().toISOString(),
        device_id: this.deviceId,
        monthly_sync_count: 0 // This will be handled by trigger
      })
      .eq('user_id', this.userId);
      
    if (error) {console.error('Update sync metadata error:', error);}
  }
  
  async cleanup() {
    console.log('[Sync] Starting cleanup process...');
    
    // Clear all timers and intervals first
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
      this.networkCheckInterval = null;
    }
    
    if (this.connectionHealthInterval) {
      clearInterval(this.connectionHealthInterval);
      this.connectionHealthInterval = null;
    }
    
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
    
    // Stop error recovery processing
    this.stopErrorRecovery();
    
    // Cancel any pending batch operations
    this.processBatch.cancel();
    
    // Unsubscribe from Supabase channel
    if (this.channel) {
      try {
        await this.channel.unsubscribe();
        console.log('[Sync] Successfully unsubscribed from channel');
      } catch (error) {
        console.error('[Sync] Error unsubscribing from channel:', error);
      }
      this.channel = null;
    }
    
    // Clear sync queue and persistent storage on cleanup
    this.clearQueue();
    this.isInitialized = false;
    this.isSyncing = false;
    this.isReconnecting = false;
    this.reconnectAttempts = 0;
    
    // Reset circuit breaker state
    this.circuitBreakerState = 'CLOSED';
    this.failureCount = 0;
    this.lastFailureTime = 0;
    
    // Reset network monitoring state
    this.isOnline = true;
    this.pendingReconnectOnOnline = false;
    
    // Reset connection health monitoring state
    this.lastHealthCheckTime = 0;
    this.consecutiveHealthCheckFailures = 0;
    
    // Clear Phoenix protocol state
    this.pendingRefs.clear();
    this.refCounter = 0;
    
    // Clear error recovery state
    this.errorRecoveryQueue.clear();
    this.errorHistory.length = 0; // Clear error history array
    this.errorCount = 0;
    
    console.log('[Sync] Cleanup completed');
  }
  
  isActive(): boolean {
    return this.isInitialized;
  }
  
  getOrganizationId(): string | null {
    return this.organizationId;
  }
}

// Export singleton instance
export const realtimeSyncService = new RealtimeSyncService();
